// ignore_for_file: inference_failure_on_collection_literal, unused_local_variable

part of 'delivery-quote.cubit.dart';

class DeliveryQuoteService {
  Future<QuoteDelivery?> getQuote(String oid, Map<String, dynamic> body) async {
    AppLog.e('body - $body');
    try {
      final response = await AppHttp.request(
        '${AppAPI.newDelivery}/$oid/delivery-quote',
        method: APIRequestMethod.POST,
        body: body,
      );
      AppLog.e('getQuote response: $response');
      AppBased.doAuditLogging(
        pageName: 'AddNewDeliveryPage',
        pageUrl: 'add-new-delivery-page',
        pageAction: 'get-quote',
        metadata: {},
      );
      return QuoteDelivery.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      // if (e.response?.statusCode == 429) {
      //   AppError.dioResponseError(DioException(type: DioExceptionType.cancel, message: '429', requestOptions: e.requestOptions));
      // } else {
      //   AppError.dioResponseError(e);
      // }
      AppError.dioResponseError(e);
    }
    return null;
  }
}
