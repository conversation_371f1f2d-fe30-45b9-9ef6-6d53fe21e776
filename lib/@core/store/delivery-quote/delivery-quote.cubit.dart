// ignore_for_file: avoid_catching_errors, avoid_positional_boolean_parameters

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/quote-delivery.model.dart';
import 'package:stickyqrbusiness/@share/apis.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

part 'delivery-quote.state.dart';
part 'delivery-quote.service.dart';

class DeliveryQuoteCubit extends Cubit<DeliveryQuoteState> {
  DeliveryQuoteCubit() : super(DeliveryQuoteState());

  void onResetStatus() {
    emit(
      state.copyWith(
        status: DeliveryQuoteStatus.Initial,
        errorMsg: '',
      ),
    );
  }

  Future<QuoteDelivery?> getQuote(String oid, Map<String, dynamic> body) async {
    emit(state.copyWith(status: DeliveryQuoteStatus.Loading));

    // final body = {
    //   'dropoff': {
    //     'street': '6645 Stockton Blvd #300, Sacramento, CA 95823, USA',
    //     'city': 'Sacramento',
    //     'country': 'US',
    //     'zipCode': '95823',
    //     'state': 'CA',
    //     'note': 'n1',
    //     'name': 'Th',
    //     'phone': '+***********'
    //   }
    // };
    try {
      final rep = await DeliveryQuoteService().getQuote(oid, body);
      if (rep != null) {
        emit(
          state.copyWith(
            quoteData: rep,
            status: DeliveryQuoteStatus.Success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: DeliveryQuoteStatus.Error,
          ),
        );
      }
      return rep;
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(
          status: DeliveryQuoteStatus.Error, errorMsg: e.message));
      return null;
    }
  }
}
