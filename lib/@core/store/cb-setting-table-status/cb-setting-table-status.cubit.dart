// ignore_for_file: avoid_catching_errors, avoid_positional_boolean_parameters

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@share/apis.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

part 'cb-setting-table-status.state.dart';
part 'cb-setting-table-status.service.dart';

class CBSettingTableStatusCubit extends Cubit<CBSettingTableStatusState> {
  CBSettingTableStatusCubit()
      : super(
          CBSettingTableStatusState()
        );

  void onResetStatus() {
    emit(state.copyWith(status: CBSettingTableStatus.Initial, errorMsg: ''));
  }

  void onEnableInProgress(bool value) {
    emit(
      state.copyWith(
        inProgress: value,
        status: CBSettingTableStatus.Edit,
      ),
    );
  }

  void onEnableAttentionRequired(bool value) {
    emit(
      state.copyWith(
        attentionRequired: value,
        status: CBSettingTableStatus.Edit,
      ),
    );
  }

  void onChangedInProgressTime(int value) {
    emit(
      state.copyWith(
        inProgressTime: value,
        status: CBSettingTableStatus.Edit,
      ),
    );
  }

  void onChangedAttentionRequiredTime(int value) {
    emit(
      state.copyWith(
        attentionRequiredTime: value,
        status: CBSettingTableStatus.Edit,
      ),
    );
  }


  Future<Business?> onUpdateCallButtonTableStatus() async {
    emit(state.copyWith(status: CBSettingTableStatus.Loading));
    try {
      final body = {
        'cbTableStatusSettings': {
          'enableInProgress': state.inProgress,
          'inProgressChangeAfterMinutes': state.inProgressTime,
          'enableAttentionRequired': state.attentionRequired,
          'attentionRequiredChangeAfterMinutes': state.attentionRequiredTime,
        },
      };
      AppLog.e('param: $body');
      final rep = await CBSettingTableStatusService().onUpdateTableStatus(body);
      if (rep != null) {
        emit(
          state.copyWith(
            status: CBSettingTableStatus.Success,
            attentionRequired: rep.cbTableStatusSettings?.enableAttentionRequired ?? false,
            inProgress: rep.cbTableStatusSettings?.enableInProgress ?? false,
            inProgressTime: rep.cbTableStatusSettings?.inProgressChangeAfterMinutes ?? 30,
            attentionRequiredTime: rep.cbTableStatusSettings?.attentionRequiredChangeAfterMinutes ?? 60,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: CBSettingTableStatus.Error,
          ),
        );
      }
      return rep;
    } on AppErrorResponse catch (e) {
      AppLog.e(
        'Error: ${e.message}',
        logType: AppLoggerType.FUNCTION,
        logValue: 'login',
        classParent: 'CBSettingTableStatusCubit',
        obj: e,
      );
      emit(
        state.copyWith(
          status: CBSettingTableStatus.Error,
          errorMsg: e.message,
        ),
      );
      return null;
    }
  }

}
