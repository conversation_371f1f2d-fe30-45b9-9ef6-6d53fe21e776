// ignore_for_file: constant_identifier_names
part of 'cb-setting-table-status.cubit.dart';

enum CBSettingTableStatus { Initial, Edit, Error, Loading, Success }

class CBSettingTableStatusState {
  final String? errorMsg;
  final CBSettingTableStatus status;
  final bool inProgress;
  final bool attentionRequired;
  final int inProgressTime;
  final int attentionRequiredTime;


  CBSettingTableStatusState({
    this.status = CBSettingTableStatus.Initial,
    this.errorMsg,
    this.inProgress = false,
    this.attentionRequired = false,
    this.inProgressTime = 30,
    this.attentionRequiredTime = 60,
  });

  CBSettingTableStatusState copyWith({
    CBSettingTableStatus? status,
    String? errorMsg,
    bool? inProgress,
    bool? attentionRequired,
    int? inProgressTime,
    int? attentionRequiredTime,
  }) {
    return CBSettingTableStatusState(
      status: status ?? this.status,
      errorMsg: errorMsg ?? this.errorMsg,
      inProgress: inProgress ?? this.inProgress,
      attentionRequired: attentionRequired ?? this.attentionRequired,
      inProgressTime: inProgressTime ?? this.inProgressTime,
      attentionRequiredTime: attentionRequiredTime ?? this.attentionRequiredTime,
    );
  }

  bool get isValidateChangeAfter => attentionRequiredTime <= inProgressTime && inProgress == true && attentionRequired == true;

  @override
  String toString() {
    return 'CBSettingTableStatusState  $status';
  }
}
