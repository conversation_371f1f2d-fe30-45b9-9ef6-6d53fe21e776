// ignore_for_file: inference_failure_on_collection_literal, unused_local_variable

part of 'cb-setting-table-status.cubit.dart';

class CBSettingTableStatusService {

  Future<Business?> onUpdateTableStatus(Map<String, dynamic> body) async {
    try {

      final response = await AppHttp.request(
        AppAPI.myBusinessDetail,
        method: APIRequestMethod.PATCH,
        body: body,
      );
      AppBased.doAuditLogging(
        pageName: 'CallButtonTableStatus',
        pageUrl: 'call-button-setting-table-status-page',
        pageAction: 'update-setting-call-button-table-status',
        metadata: body,
      );

      return Business.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      AppError.dioResponseError(e);
    }
    return null;
  }

}
