// ignore_for_file: avoid_dynamic_calls, unrelated_type_equality_checks, inference_failure_on_collection_literal

part of 'auth_bloc.dart';

class AuthState {
  AuthState({
    this.authStatus = AuthenticationStatus.initial,
    this.refreshToken,
    this.accessToken,
    this.user,
    this.userId,
    this.isUnauthenticatedGoHome,
    this.business,
    this.permissions,
    this.businessRoleId,
    this.features,
  });
  final AuthenticationStatus? authStatus;
  final String? refreshToken;
  final String? accessToken;
  final String? userId;
  final User? user;
  final Business? business;
  final List<Permission>? permissions;
  final String? businessRoleId;
  final List<Feature>? features;

  /// If [authStatus] is [AuthenticationStatus.authenticated]
  final bool? isUnauthenticatedGoHome;

  AuthState copyWith({
    AuthenticationStatus? authStatus,
    String? accessToken,
    String? refreshToken,
    String? userId,
    User? user,
    bool? isUnauthenticatedGoHome,
    Business? business,
    List<Permission>? permissions,
    String? businessRoleId,
    List<Feature>? features,
  }) {
    return AuthState(
      businessRoleId: businessRoleId ?? this.businessRoleId,
      authStatus: authStatus ?? this.authStatus,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      userId: userId ?? this.userId,
      user: user ?? this.user,
      business: business ?? this.business,
      isUnauthenticatedGoHome: isUnauthenticatedGoHome ?? this.isUnauthenticatedGoHome,
      permissions: permissions ?? this.permissions,
      features: features ?? this.features,
    );
  }

  @override
  String toString() {
    return 'AuthState $authStatus $isLoggedIn $userId ${user?.id} $isUnauthenticatedGoHome';
  }

  /// Returns true if the state is authenticated
  bool get isAuthenticated => authStatus == AuthenticationStatus.authenticated;

  bool get isLoggedIn => accessToken != null;

  // String? get uid => user?.uid.toString();
  String get uid => userId ?? '';
  String get bid => business?.id ?? '';
  String get getStartedCodeFromLinks => business?.getStartedCodeFromLinks ?? '';
  String? get nameBusiness => business?.name;
  String? get businessTimeZone => business?.timeZone?.toUpperCase() == 'Asia/SaiGon'.toUpperCase() ? 'Asia/Ho_Chi_Minh' : business?.timeZone;
  String? get logoBusiness => business?.logo;
  String? get logoBusinessID => business?.avatar?.publicId;
  String? get planBusiness => business?.currentPlan;
  bool get businessOwner => business?.role?.toUpperCase() == 'OWNER';
  String? get languageBusiness => business?.language?.toLowerCase();
  String? get planMaxCustomers => business?.planMaxCustomers;
  String? get totalActiveCustomers => business?.totalActiveCustomers;
  bool? get showScanPrintersManual => business?.showScanPrintersManual ?? false;
  String? get currencyBusiness => business?.currency ?? 'USD';
  bool? get acceptCustomerPayment => business?.acceptCustomerPayment ?? false;

  bool? get enableCallButton => business?.enableCallButton ?? false;
  bool? get enableCallButtonSound => business?.enableCallButtonSound ?? false;
  int? get callButtonSoundRepeat => business?.callButtonSoundRepeat;
  int? get callButtonSoundName => business?.callButtonSoundName;

  String? get thanksTitle => business?.callButtonThankYouCustomize?.title;
  String? get thanksMessage => business?.callButtonThankYouCustomize?.message;
  String? get thanksCTAButtonTitle => business?.callButtonThankYouCustomize?.ctaButtonTitle;
  String? get thanksCTAButtonLink => business?.callButtonThankYouCustomize?.ctaButtonLink;
  List<CallButtonThankYouButton>? get callButtonThankYouButtons => business?.callButtonThankYouButtons;
  bool? get enablePointClaims => business?.acceptCustomerClaimsPoint;
  bool? get enableReferralProgram => business?.enableReferralProgram;
  bool? get orderAllowOnlineOrdering => business?.orderAllowOnlineOrdering ?? false;
  bool? get isNewHomePage => business?.isNewHomePage;
  bool get isAllowFlexibleHomePage => business?.isAllowFlexibleHomePage ?? false;
  String get newHomePageDefaultPage => business?.newHomePageDefaultPage ?? 'rewards';

  List<Permission>? get permissionsStaff => permissions;
  String get roleIdBusiness => businessRoleId ?? '';

  List<Feature>? get menuFeatures => features ?? [];
  bool get isFree => business?.currentPlan?.toLowerCase() == 'free' || business?.currentPlan?.toLowerCase() == 'standard' || business?.currentPlan?.toLowerCase() == 'gold';
  bool get isPro => business?.currentPlan?.toLowerCase() == 'pro' || business?.currentPlan?.toLowerCase() == 'enterprise' || business?.currentPlan?.toLowerCase() == 'platinum';
  // String? get addressFull => '${business?.address?.street}, ${business?.address?.city}, ${business?.address?.state},  ${business?.address?.country}'; //${business?.address?.zipCode},
  String? get addressFull {
    final parts = [
      business?.address?.street,
      business?.address?.city,
      business?.address?.state,
      business?.address?.country,
    ];

    final filtered = parts.where((part) => part != null && part.trim().isNotEmpty).toList();

    return filtered.isEmpty ? null : filtered.join(', ');
  }

  bool checkRole(String key) {
    if (businessOwner) {
      return true;
    }
    final checkKey = permissionsStaff?.firstWhereOrNull((permission) => permission.key?.toLowerCase() == key.toLowerCase() && permission.actions != [] && permission.actions != null && permission.actions!.isNotEmpty);
    if (checkKey != null) {
      return true;
    }

    return false;
  }

// Phương thức mới kiểm tra nhiều key
  bool checkMultipleRoles(List<String> keys) {
    if (businessOwner) {
      return true;
    }
    for (final String key in keys) {
      final checkKey = permissionsStaff?.firstWhereOrNull((permission) => permission.key?.toLowerCase() == key.toLowerCase() && permission.actions != [] && permission.actions != null && permission.actions!.isNotEmpty);
      if (checkKey != null) {
        return true;
      }
    }
    return false;
  }

  bool checkPermission(String key, int roleStaff) {
    if (businessOwner) {
      return true;
    }
    final checkKey = permissionsStaff?.firstWhereOrNull((permission) => permission.key?.toLowerCase() == key.toLowerCase() && permission.actions != [] && permission.actions != null && permission.actions!.isNotEmpty);
    if (checkKey != null) {
      final checkRoleStaff = checkKey.actions?.firstWhereOrNull((role) => role == 0 || role == roleStaff);

      if (checkRoleStaff != null) {
        return true;
      }
    } else {
      return false;
    }

    return false;
  }

  bool checkPermissions(String key, List<int> roleStaff) {
    if (businessOwner) {
      return true;
    }
    final checkKey = permissionsStaff?.firstWhereOrNull((permission) => permission.key?.toLowerCase() == key.toLowerCase() && permission.actions != [] && permission.actions != null && permission.actions!.isNotEmpty);
    if (checkKey != null) {
      final checkRoleStaff = checkKey.actions?.firstWhereOrNull((role) => role == 0);
      if (checkRoleStaff != null) return true;

      final set1 = (checkKey.actions ?? []).toSet();
      final set2 = roleStaff.toSet();

      if (set1.intersection(set2).isNotEmpty) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  double checkUpdatePercent() {
    const double percentTotal = 8;
    double percent = 8;
    if (business?.avatar == '' || business?.avatar == null) {
      percent -= 1;
    }
    if (business?.currency == '' || business?.currency == null) {
      percent -= 1;
    }
    if (business?.address?.country == '' || business?.address?.country == null) {
      percent -= 1;
    }
    if (business?.address?.street == '' || business?.address?.street == null) {
      percent -= 1;
    }
    if (business?.address?.city == '' || business?.address?.city == null) {
      percent -= 1;
    }
    if (business?.address?.state == '' || business?.address?.state == null) {
      percent -= 1;
    }
    if (business?.address?.zipCode == '' || business?.address?.zipCode == null) {
      percent -= 1;
    }
    if (business?.language == '' || business?.language == null) {
      percent -= 1;
    }
    return percent / percentTotal * 100;
  }
}
