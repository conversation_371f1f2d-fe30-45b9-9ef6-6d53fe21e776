part of 'orders-history.cubit.dart';

enum OrderHistoryStatus { Initial, Edit, Progress, Error, Loading, Success, Closed }

enum OrderHistoryFilterDateTime {
  ALL,
  TODAY,
  // THIS_WEEK,
  THIS_MONTH,
  LAST_7_DATE,
  LAST_30_DATE,
  LAST_3_MONTHS,
  LAST_6_MONTHS,
  // MONTH_TO_DATE,
  YEAR_TO_DATE,
  CUSTOM,
}

enum OrderHistoryFilterStatus { ALL, COMPLETED, CANCELLED }

class OrderHistoryState {
  final OrderHistoryStatus status;
  final OrderHistoryFilterDateTime dateType;
  final OrderHistoryFilterStatus filterStatus;
  final List<Order>? orders;
  final List<Order>? ordersBackUp;
  final Order? selectedOrder;
  final String? selectedOrderId;
  final String? errorMsg;
  final bool isLoading;
  final bool isHasMore;
  final int page;
  final String? dateStart;
  final String? dateEnd;
  final String? searchText;
  final String? timeZone;

  const OrderHistoryState({
    this.status = OrderHistoryStatus.Initial,
    this.dateType = OrderHistoryFilterDateTime.ALL,
    this.filterStatus = OrderHistoryFilterStatus.ALL,
    this.orders,
    this.ordersBackUp,
    this.selectedOrder,
    this.selectedOrderId,
    this.isLoading = false,
    this.errorMsg,
    this.isHasMore = false,
    this.page = 0,
    this.dateStart,
    this.dateEnd,
    this.searchText,
    this.timeZone,
  });

  OrderHistoryState copyWith({
    OrderHistoryStatus? status,
    List<Order>? orders,
    List<Order>? ordersBackUp,
    Order? selectedOrder,
    String? selectedOrderId,
    bool? isLoading,
    String? errorMsg,
    bool? isHasMore,
    int? page,
    OrderHistoryFilterDateTime? dateType,
    OrderHistoryFilterStatus? filterStatus,
    String? dateStart,
    String? dateEnd,
    String? searchText,
    String? timeZone,
  }) {
    return OrderHistoryState(
      timeZone: timeZone ?? this.timeZone,
      dateEnd: dateEnd ?? this.dateEnd,
      dateStart: dateStart ?? this.dateStart,
      filterStatus: filterStatus ?? this.filterStatus,
      dateType: dateType ?? this.dateType,
      selectedOrderId: selectedOrderId ?? this.selectedOrderId,
      status: status ?? this.status,
      ordersBackUp: ordersBackUp ?? this.ordersBackUp,
      orders: orders ?? this.orders,
      selectedOrder: selectedOrder ?? this.selectedOrder,
      isLoading: isLoading ?? this.isLoading,
      errorMsg: errorMsg ?? this.errorMsg,
      isHasMore: isHasMore ?? this.isHasMore,
      page: page ?? this.page,
      searchText: searchText ?? this.searchText,
    );
  }

  List<Object?> get props => [orders, selectedOrder, isLoading, errorMsg];

  @override
  String toString() {
    return 'OrderHistoryState  $status';
  }

  List<Order> onChangedSearch() {
    if (searchText?.trim() != '' && searchText?.trim() != null) {
      final orders = searchOrders(ordersBackUp ?? [], searchText ?? '');

      return orders;
    } else {
      return ordersBackUp ?? [];
    }
  }

  List<Order> searchOrders(List<Order> orders, String query) {
    try {
      if (query == '' || query == null) {
        return orders;
      }

      final queryLower = query.toLowerCase();

      return orders.where((order) {
        return order.orderNumber!.toLowerCase().contains(queryLower) || (order.customer != null && order.customer?.displayName != null && order.customer!.displayName!.toLowerCase().contains(queryLower)) || (order.customer != null && order.customer?.phone != null && order.customer!.phone!.toLowerCase().contains(queryLower));
      }).toList();
    } catch (e) {
      AppLog.e('onChangedSearch errrrr ???: $e');

      return orders;
    }
  }
}
