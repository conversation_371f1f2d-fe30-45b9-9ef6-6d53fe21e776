// ignore_for_file: avoid_catching_errors, avoid_positional_boolean_parameters

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/models/order.model.dart';
import 'package:stickyqrbusiness/@core/models/orders.model.dart';
import 'package:stickyqrbusiness/@core/store/orders-history/orders-history.service.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

part 'orders-history.state.dart';

class OrderHistoryCubit extends Cubit<OrderHistoryState> {
  OrderHistoryCubit() : super(const OrderHistoryState());

  final _service = OrderHistoryService();
  Future<Orders?> getOrders({
    int? page,
    int rows = 50,
    bool withLoading = true,
    OrderHistoryStatus? status,
    int maxLoops = 5,
    int loopCount = 0,
  }) async {
    try {
      if (withLoading) {
        if (!isClosed) {
          emit(
            state.copyWith(
              status: OrderHistoryStatus.Loading,
              selectedOrder: Order(),
            ),
          );
        }
      }

      final List<Order> current = state.orders ?? [];
      if (page == 0) {
        state.orders?.clear();
      }
      final int pageIndex = page ?? state.page + 1;
      final Map<String, dynamic> param = {
        'page': pageIndex,
        'take': rows,
      };

      if (state.filterStatus != OrderHistoryFilterStatus.ALL) {
        param['status'] = state.filterStatus.name;
      }
      if (state.dateType != OrderHistoryFilterDateTime.ALL) {
        param['dateType'] = state.dateType.name;

        if (state.dateType == OrderHistoryFilterDateTime.TODAY || state.dateType == OrderHistoryFilterDateTime.THIS_MONTH || state.dateType == OrderHistoryFilterDateTime.LAST_7_DATE || state.dateType == OrderHistoryFilterDateTime.LAST_30_DATE || state.dateType == OrderHistoryFilterDateTime.LAST_3_MONTHS || state.dateType == OrderHistoryFilterDateTime.LAST_6_MONTHS || state.dateType == OrderHistoryFilterDateTime.YEAR_TO_DATE || state.dateType == OrderHistoryFilterDateTime.CUSTOM) {
          param['dateType'] = OrderHistoryFilterDateTime.CUSTOM.name;

          param['dateStart'] = DateTimeHelper.convertToUtcDate(state.dateStart != null ? DateTime.parse(state.dateStart ?? '') : DateTimeHelper.currentTime(state.timeZone), state.timeZone ?? '')?.toIso8601String();
          param['dateEnd'] = DateTimeHelper.convertToUtcDate(state.dateEnd != null ? DateTime.parse(state.dateEnd ?? '') : DateTimeHelper.currentTime(state.timeZone), state.timeZone ?? '')?.toIso8601String();
        }
      }

      final response = await _service.getOrdersHistory(param);
      if (response != null) {
        final List<Order> newList = current + (response.data ?? []);
        final bool isHasMore = response.meta?.hasMore ?? false;

        if (!isClosed) {
          emit(
            state.copyWith(
              orders: newList,
              ordersBackUp: newList,
              page: pageIndex,
              isHasMore: isHasMore,
              status: status ?? OrderHistoryStatus.Success,
              isLoading: isHasMore,
            ),
          );
        }

        // Kiểm tra điều kiện để gọi lại hàm
        if (isHasMore && loopCount < maxLoops && !isClosed) {
          return await getOrders(
            page: pageIndex + 1, // Tăng page lên 1
            rows: rows,
            withLoading: false, // Không hiển thị loading cho các lần gọi lại
            status: status,
            maxLoops: maxLoops,
            loopCount: loopCount + 1, // Tăng số lần lặp
          );
        }
      } else {
        if (!isClosed) {
          emit(
            state.copyWith(
              orders: response?.data,
              ordersBackUp: response?.data,
              page: page,
              isHasMore: false,
              status: OrderHistoryStatus.Error,
              isLoading: false,
            ),
          );
        }
      }
    } on AppErrorResponse catch (e) {
      if (!isClosed) {
        emit(
          state.copyWith(
            status: OrderHistoryStatus.Error,
            errorMsg: e.message,
          ),
        );
      }
    }
    return null;
  }

  // Future<Orders?> getOrders1({
  //   int? page,
  //   int rows = 100,
  //   // int rows = 20,
  //   bool withLoading = true,
  //   OrderHistoryStatus? status,
  // }) async {
  //   try {
  //     if (withLoading) {
  //       emit(
  //         state.copyWith(
  //           status: OrderHistoryStatus.Loading,
  //           selectedOrder: Order(),
  //         ),
  //       );
  //     }

  //     final List<Order> current = state.orders ?? [];
  //     if (page == 0) {
  //       state.orders?.clear();
  //     }
  //     final int pageIndex = page ?? state.page + 1;
  //     final Map<String, dynamic> param = {
  //       'page': pageIndex,
  //       'take': rows,
  //     };

  //     if (state.filterStatus != OrderHistoryFilterStatus.ALL) {
  //       param['status'] = state.filterStatus.name;
  //     }
  //     if (state.dateType != OrderHistoryFilterDateTime.ALL) {
  //       param['dateType'] = state.dateType.name;
  //       if (state.dateType == OrderHistoryFilterDateTime.CUSTOM) {
  //         param['dateStart'] = DateTimeHelper.convertToUtcStartDate(state.dateStart != null ? DateTime.parse(state.dateStart ?? '') : DateTimeHelper.currentTime(state.timeZone), state.timeZone ?? '')?.toIso8601String();
  //         param['dateEnd'] = DateTimeHelper.convertToUtcEndDate(state.dateEnd != null ? DateTime.parse(state.dateEnd ?? '') : DateTimeHelper.currentTime(state.timeZone), state.timeZone ?? '')?.toIso8601String();
  //       } else {}
  //     }
  //     // AppLog.e('orders param == $param');

  //     final response = await _service.getOrdersHistory(param);
  //     // AppLog.e('orders response == ${jsonEncode(response)}');
  //     if (response != null) {
  //       final List<Order> newList = current + (response.data ?? []);
  //       final bool isHasMore = response.meta?.hasMore ?? false;
  //       emit(
  //         state.copyWith(
  //           orders: newList,
  //           ordersBackUp: newList,
  //           page: pageIndex,
  //           isHasMore: isHasMore,
  //           status: status ?? OrderHistoryStatus.Success,
  //           isLoading: isHasMore,
  //         ),
  //       );
  //     } else {
  //       emit(
  //         state.copyWith(
  //           orders: response?.data,
  //           ordersBackUp: response?.data,
  //           page: page,
  //           isHasMore: false,
  //           status: OrderHistoryStatus.Error,
  //           isLoading: false,
  //         ),
  //       );
  //     }
  //   } on AppErrorResponse catch (e) {
  //     emit(
  //       state.copyWith(
  //         status: OrderHistoryStatus.Error,
  //         errorMsg: e.message,
  //       ),
  //     );
  //   }
  //   return null;
  // }

  void updateOrderInList(Order updatedOrder) {
    final List<Order> currentOrders = state.orders ?? [];
    final index = currentOrders.indexWhere((order) => order.id == updatedOrder.id);

    if (index != -1) {
      final updatedOrdersList = List<Order>.from(currentOrders);
      updatedOrdersList[index] = updatedOrder;

      emit(state.copyWith(orders: updatedOrdersList, ordersBackUp: updatedOrdersList, status: OrderHistoryStatus.Edit));
    }
  }

  void onChangeIsLoading(bool value) {
    emit(state.copyWith(isLoading: value));
  }

  void selectOrder(Order order) {
    emit(state.copyWith(selectedOrder: order));
  }

  void onChangedDateTimeType(OrderHistoryFilterDateTime dateTime) {
    emit(state.copyWith(dateType: dateTime));
  }

  void onChangedStatus(OrderHistoryFilterStatus status) {
    emit(state.copyWith(filterStatus: status));
  }

  void onChangedDateFrom(String? value) {
    emit(state.copyWith(dateStart: value));
  }

  void onChangedDateTo(String? value) {
    emit(state.copyWith(dateEnd: value));
  }

  void onChangedTimeZone(String? value) {
    emit(state.copyWith(timeZone: value));
  }

  void onChangedSearch(String? value) {
    emit(state.copyWith(searchText: value));
  }

  void onChangedPageClear({int? value = 0}) {
    emit(state.copyWith(page: value));
  }

  void onChangedSearchTextClear() {
    emit(state.copyWith(
      searchText: '',
      status: OrderHistoryStatus.Initial,
    ));
  }

  void clearSelectedOrder() {
    emit(state.copyWith(selectedOrder: null));
  }

  void onResetStatus() {
    emit(
      state.copyWith(
        status: OrderHistoryStatus.Initial,
        errorMsg: '',
        isLoading: false,
      ),
    );
  }
}
