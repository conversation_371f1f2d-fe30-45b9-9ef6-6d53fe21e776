part of 'new-delivery.cubit.dart';

class NewDeliveryService {
  Future<Order?> onCreateNewDelivery(String oid, {Map<String, dynamic>? body}) async {
    try {
      final response = await AppHttp.request(
        '${AppAPI.newDelivery}/$oid/new-delivery',
        method: APIRequestMethod.POST,
        body: body,
      );
      AppLog.e('response new-delivery: ${response.data}');
      AppBased.doAuditLogging(
        pageName: 'NewDeliveryService',
        pageUrl: 'new-delivery',
        pageAction: 'createNewDelivery',
      );

      final data = Order.fromJson(response.data as Map<String, dynamic>);
      return data;
    } catch (e) {
      AppError.dioResponseError(e);
    }
    return null;
  }

  Future<Order?> onCancelNewDelivery(String oid, {Map<String, dynamic>? body}) async {
    try {
      final response = await AppHttp.request(
        '${AppAPI.newDelivery}/$oid/cancel-delivery-express',
        method: APIRequestMethod.POST,
        // ignore: inference_failure_on_collection_literal
        body: {},
      );
      AppLog.e('response new-delivery: ${response.data}');
      AppBased.doAuditLogging(
        pageName: 'NewDeliveryService',
        pageUrl: 'new-delivery',
        pageAction: 'createNewDelivery',
      );

      final data = Order.fromJson(response.data as Map<String, dynamic>);
      return data;
    } catch (e) {
      AppError.dioResponseError(e);
    }
    return null;
  }
}
