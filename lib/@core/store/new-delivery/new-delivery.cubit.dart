// ignore_for_file: avoid_catching_errors

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/controls/controls.dart';
import 'package:stickyqrbusiness/@share/apis.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

part 'new-delivery.state.dart';
part 'new-delivery.service.dart';

class NewDeliveryCubit extends Cubit<NewDeliveryState> {
  NewDeliveryCubit() : super(const NewDeliveryState(
    busPhone: PhoneControl.pure(),
            // currentCountry: '',
            currentCode: '',
            flag: '',
            deliveryAddress: TextInputControl.pure(),
  ));

  void onResetStatus() {
    emit(state.copyWith(status: NewDeliveryStatus.Initial, errorMsg: ''));
  }

  void onRemoveErrMsg(String msg) {
    emit(state.copyWith(errorMsg: msg));
  }

  void onChangedDeliveryType(String value) {
    emit(state.copyWith(deliveryType: value));
  }

  void onChangedScheduleAt(String value) {
    emit(state.copyWith(scheduledAt: value));
    print('scheduledAt: $value /// state.scheduledAt: ${state.scheduledAt}');
  }

  void onChangedDropoffOption(String value) {
    emit(state.copyWith(dropoffOption: value));
  }

  void onChangedName(String value) {
    emit(
      state.copyWith(name: TextInputControl.dirty(value)),
    );
  }

  void onChangedPhoneNumber(String value) {
    emit(
      state.copyWith(
        busPhone: PhoneControl.dirty(value),
        status: NewDeliveryStatus.Initial,
      ),
    );
  }

  void onChangedCurrentCode(String value) {
    emit(
      state.copyWith(
        currentCode: value,
        status: NewDeliveryStatus.Initial,
      ),
    );
  }

  void onChangedFlag(String value) {
    emit(
      state.copyWith(
        flag: value,
        status: NewDeliveryStatus.Initial,
      ),
    );
  }

  void onChangedLocationNotes(String value) {
    emit(
      state.copyWith(
        locationNotes: TextInputControl.dirty(value),
        status: NewDeliveryStatus.Initial,
      ),
    );
  }

  void onChangedDeliveryNotes(String value) {
    emit(
      state.copyWith(
        deliveryNotes: TextInputControl.dirty(value),
        status: NewDeliveryStatus.Initial,
      ),
    );
  }

  void onChangedLocationName(String value) {
    emit(
      state.copyWith(
        locationName: TextInputControl.dirty(value),
        status: NewDeliveryStatus.Initial,
      ),
    );
  }

  void onChangedTipSelect(String value) {
    emit(
      state.copyWith(
        tipSelected: value,
        status: NewDeliveryStatus.Initial,
      ),
    );
    print('tip: $value /// state.tip: ${state.tipSelected}');
  }

  void onChangedTipAmount(double? value) {
    emit(
      state.copyWith(
        tipAmount: value,
        status: NewDeliveryStatus.Initial,
      ),
    );
    print('tip: $value /// state.tip: ${state.tipSelected}');
  }

  // Future<void> onChangedDeliveryAddress(String value) async {
  //   emit(
  //     state.copyWith(
  //       deliveryAddress: TextInputControl.dirty(value),
  //       status: NewDeliveryStatus.Edit,
  //     ),
  //   );
  // }

  Future<void> onChangedDeliveryAddress(Place value) async {
    if (value != null) {
      emit(
        state.copyWith(
          place: value,
          deliveryAddress: TextInputControl.dirty(value.long ?? ''),
          status: NewDeliveryStatus.Edit,
        ),
      );
    } else {
      emit(
        state.copyWith(
          place: null,
          deliveryAddress: const TextInputControl.dirty(''),
          status: NewDeliveryStatus.Edit,
        ),
      );
    }
  }

  Future<Order?> onCreateNewDelivery({
    required String oid,
    required Map<String, dynamic> body,
    bool isLoading = true,
  }) async {
    try {
      if (isLoading) {
        emit(state.copyWith(status: NewDeliveryStatus.Loading));
      }

      final order =
          await NewDeliveryService().onCreateNewDelivery(oid, body: body);
      if (order != null) {
        emit(
          state.copyWith(
            order: order,
            status: NewDeliveryStatus.Success,
          ),
        );
      }
      return order;
    } on AppErrorResponse catch (e) {
      emit(
        state.copyWith(
          status: NewDeliveryStatus.Error,
          errorMsg: e.message,
        ),
      );
    }
    return null;
  }

  Future<Order?> onCancelDelivery({
    required String oid,
    bool isLoading = true,
  }) async {
    try {
      if (isLoading) {
        emit(state.copyWith(status: NewDeliveryStatus.Loading));
      }

      final order =
          await NewDeliveryService().onCancelNewDelivery(oid);
      if (order != null) {
        emit(
          state.copyWith(
            order: order,
            status: NewDeliveryStatus.Canceled,
          ),
        );
      }
      return order;
    } on AppErrorResponse catch (e) {
      emit(
        state.copyWith(
          status: NewDeliveryStatus.Error,
          errorMsg: e.message,
        ),
      );
    }
    return null;
  }

}
