// ignore_for_file: avoid_positional_boolean_parameters, avoid_catching_errors, inference_failure_on_instance_creation

import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/_error-handle.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.service.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

part 'call-button.state.dart';

class CallButtonCubit extends Cubit<CallButtonState> {
  CallButtonCubit() : super(const CallButtonState());

  final _service = CallButtonGetSessionsService();

  void onChangeStyleView(CallButtonStyleView style) {
    emit(
      state.copyWith(
        status: CallButtonStatus.Edit,
        styleView: style,
      ),
    );
  }

  void onChangeExpand(bool value) {
    emit(
      state.copyWith(
        status: CallButtonStatus.Edit,
        isExpanded: value,
      ),
    );
  }

  void onResetStatus() {
    emit(
      state.copyWith(
        status: CallButtonStatus.Initial,
        styleView: CallButtonStyleView.Gridview,
        errorMsg: '',
      ),
    );
  }

  void onUpdateListTable(String rId) {
    final currentSections = state.allSections ?? [];
    for (final CallButtonSections section in currentSections) {
      if (section.tables != null) {
        for (final CallButtonTable table in section.tables!) {
          if (table.requests != null) {
            table.requests!.removeWhere((request) => request.id == rId);
          }
        }
      }
    }
    // AppLog.e('currentSections update == ${jsonEncode(currentSections)}');
    emit(
      state.copyWith(
        status: CallButtonStatus.Success,
        allSections: currentSections,
        filterSections: filterSections(currentSections),
      ),
    );
  }

  void onClearAllRequestsTable(String callButtonId) {
    final currentSections = state.allSections ?? [];
    for (final CallButtonSections section in currentSections) {
      if (section.tables != null) {
        for (final CallButtonTable table in section.tables!) {
          if (table.callButtonId == callButtonId && table.requests != null) {
            table.requests!.clear();
          }
        }
      }
    }
    emit(
      state.copyWith(
        status: CallButtonStatus.Success,
        allSections: currentSections,
        filterSections: filterSections(currentSections),
      ),
    );
  }

  void onCloseOutTable(String tableId) {
    final updatedSections = List<CallButtonSections>.from(state.allSections ?? []);

    for (int i = updatedSections.length - 1; i >= 0; i--) {
      final CallButtonSections section = updatedSections[i];
      if (section.tables != null) {
        for (int j = section.tables!.length - 1; j >= 0; j--) {
          final CallButtonTable table = section.tables![j];
          if (table.id == tableId) {
            section.tables!.removeAt(j);
            if (section.tables!.isEmpty) {
              updatedSections.removeAt(i);
            }
            // print('all sections 11==== ${jsonEncode(state.allSections)}');
            // print('all sections 22==== ${jsonEncode(updatedSections)}');
            break;
          }
        }
      }
    }
    emit(
      state.copyWith(
        status: CallButtonStatus.Success,
        allSections: updatedSections,
        filterSections: filterSections(updatedSections),
      ),
    );
    // print('all sections ==== ${jsonEncode(updatedSections)}');
  }

  Future<List<CallButtonSections>?> getSections({bool withLoading = true}) async {
    try {
      if (withLoading) {
        emit(state.copyWith(status: CallButtonStatus.Loading));
      }

      final response = await _service.getSessions();
      if (response != null) {
        emit(
          state.copyWith(
            allSections: response,
            allIDOld: getAllIdQuery(response),
            status: CallButtonStatus.Success,
            filterSections: filterSections(response),
          ),
        );
        AppLog.d('get sections = ${jsonEncode(response)}');
      } else {
        emit(
          state.copyWith(
            allSections: null,
            filterSections: [],
            status: CallButtonStatus.Error,
          ),
        );
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: CallButtonStatus.Error, errorMsg: e.message));
    }
    return null;
  }

// TỰ ĐỘNG GỌI LẠI CALLBUTTON => GỌI ÂM THANH NẾU KHÁC NHAU
  Future<bool?> autoGetSections() async {
    try {
      final allIDOld = state.allIDOld ?? [];
      final response = await _service.getSessions();
      if (response != null) {
        final allIDNew = getAllIdQuery(response);
        final bool isEqual = const ListEquality().equals(allIDOld, allIDNew);
        emit(
          state.copyWith(
            allSections: response,
            allIDOld: getAllIdQuery(response),
            status: CallButtonStatus.Success,
          ),
        );
        final checkCallSound = isEqual == false && allIDNew.length > allIDOld.length;
        return checkCallSound;
      } else {
        emit(
          state.copyWith(
            allSections: null,
            filterSections: [],
            allIDOld: [],
            status: CallButtonStatus.Error,
          ),
        );
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: CallButtonStatus.Error, errorMsg: e.message));
    }
    return false;
  }

  List<String> getAllIdQuery(List<CallButtonSections> response) {
    final List<String> requestIds = [];

    for (final CallButtonSections floor in response) {
      for (final CallButtonTable table in floor.tables ?? []) {
        for (final CallButtonRequest request in table.requests ?? []) {
          requestIds.add(request.id ?? '');
        }
      }
    }
    // AppLog.e('getAllIdQuery: $requestIds');
    return requestIds;
  }

  List<CallButtonSections> filterSections(List<CallButtonSections> allSections) {
    final List<CallButtonSections> filteredSections = allSections
        .map((section) {
          final filteredTables = section.tables!
              .where((table) => table.isInactive == false // &&
                  // table.requests != null &&
                  // table.requests!.isNotEmpty,
                  )
              .toList();
          return filteredTables.isNotEmpty
              ? CallButtonSections(
                  id: section.id,
                  name: section.name,
                  icon: section.icon,
                  index: section.index,
                  createdAt: section.createdAt,
                  tables: filteredTables,
                )
              : null;
        })
        .where((section) => section != null)
        .cast<CallButtonSections>()
        .toList();
    return filteredSections;
  }

  List<CallButtonTable> filterTables(List<CallButtonTable> allTables) {
    return allTables
        .where((table) => table.isInactive == false // &&
            // table.requests != null &&
            // table.requests!.isNotEmpty,
            )
        .toList();
  }

  List<String> findAndAddIcons(List<CallButtonRequest> requests) {
    final List<String> icons = [];
    for (final item in requests) {
      if (item.isCustomRequest != null && item.isCustomRequest == true) {
        if (item.customRequestIcon != null && item.customRequestIcon != '') {
          icons.add(item.customRequestIcon ?? '');
        }
      } else if (item.service?.icon != null && item.service?.icon != '') {
        icons.add(item.service!.icon!);
      }
    }
    return icons;
  }

  List<Checkin> filterUserCheckin(List<Checkin> users) {
    return users.where((user) => user.user == null || user.user != null && user.user?.isActive == true).toList();
  }
}
