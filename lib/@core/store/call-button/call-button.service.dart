import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@share/apis.dart';
// import 'package:stickyqrbusiness/@utils/utils.dart';

class CallButtonGetSessionsService {
  Future<List<CallButtonSections>?> getSessions() async {
    try {
      final response = await AppHttp.request(
        AppAPI.callButtonGetSessions,
        method: APIRequestMethod.GET,
      );
      AppBased.doAuditLogging(
        pageName: 'Home',
        pageUrl: 'call-button',
        pageAction: 'get-all-sessions check date',
      );
      // AppLog.e('CallButtonGetSessionsService: $response');
      // if (response.statusCode == 200 && response.data != null && response.data != '') {
      final List<dynamic> items = (response.data as List?) ?? [];
      final data = items.map((e) => CallButtonSections.fromJson(e as Map<String, dynamic>)).toList();
      return data;
      // }
      // return null;
    } catch (e) {
      AppError.dioResponseError(e);
    }
    return null;
  }
}
