// ignore_for_file: inference_failure_on_collection_literal

import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/close-out-inactive.model.dart';
import 'package:stickyqrbusiness/@share/apis.dart';

class CloseOutInactiveService {
  Future<List<CloseOutInactive>?> onCloseOutInactiveTable() async {
    try {
      final response = await AppHttp.request(
        AppAPI.callButtonCloseOutInactive,
        method: APIRequestMethod.PATCH,
        body: {},
      );
      AppBased.doAuditLogging(
        pageName: 'CallButtonDetail',
        pageUrl: 'call-button-detail',
        pageAction: 'close-out-inactive-table',
      );
      if (response.statusCode == 200) {
        final List<dynamic> items = (response.data as List?) ?? [];
        final data = items.map((e) => CloseOutInactive.fromJson(e as Map<String, dynamic>)).toList();
        return data;
      }
      return null;
    } catch (e) {
      AppError.dioResponseError(e);
    }
    return null;
  }

  Future<List<CloseOutInactive>?> onCloseOutInactiveTableSelected(Map<String, dynamic> body) async {
    try {
      final response = await AppHttp.request(
        AppAPI.callButtonCloseOutInactive,
        method: APIRequestMethod.PATCH,
        body: body,
      );
      AppBased.doAuditLogging(
        pageName: 'CallButtonDetail',
        pageUrl: 'call-button-detail',
        pageAction: 'close-out-inactive-table',
      );
      if (response.statusCode == 200) {
        final List<dynamic> items = (response.data as List?) ?? [];
        final data = items.map((e) => CloseOutInactive.fromJson(e as Map<String, dynamic>)).toList();
        return data;
      }
      return null;
    } catch (e) {
      AppError.dioResponseError(e);
    }
    return null;
  }
}
