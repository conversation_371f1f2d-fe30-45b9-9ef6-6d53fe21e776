// ignore_for_file: avoid_positional_boolean_parameters, avoid_catching_errors

import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/_error-handle.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/models/close-out-inactive.model.dart';
import 'package:stickyqrbusiness/@core/store/call-button-close-out-inactive/call-button-close-out-inactive.service.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';

part 'call-button-close-out-inactive.state.dart';

class CallButtonCloseOutInactiveCubit extends Cubit<CallButtonCloseOutInactiveState> {
  CallButtonCloseOutInactiveCubit() : super(const CallButtonCloseOutInactiveState());

  final _service = CloseOutInactiveService();

  void onResetStatus() {
    emit(
      state.copyWith(
        status: CallButtonCloseOutInactiveStatus.Initial,
        errorMsg: '',
        slectedIds: [],
        tablesInactive: [],
      ),
    );
  }

  void onRemoveMsg() {
    emit(
      state.copyWith(
        status: CallButtonCloseOutInactiveStatus.Initial,
        errorMsg: '',
      ),
    );
  }

  Future<List<CloseOutInactive>?> onCloseOutInactive() async {
    emit(state.copyWith(status: CallButtonCloseOutInactiveStatus.Loading));
    try {
      final response = await _service.onCloseOutInactiveTable();
      if (response != null) {
        emit(
          state.copyWith(
            data: response,
            isSlectedAll: false,
            slectedIds: [],
            tablesInactive: [],
            status: CallButtonCloseOutInactiveStatus.Success,
          ),
        );
      }
    } on AppErrorResponse catch (e) {
      emit(
        state.copyWith(
          status: CallButtonCloseOutInactiveStatus.Error,
          errorMsg: e.message,
        ),
      );
    }
    return null;
  }

  void onChangeSlectedAll(bool value) {
    final allTableIds = (state.tablesInactive ?? [])
        .map((table) => table.id ?? '')
        .where((id) => id.isNotEmpty)
        .toList();

    emit(
      state.copyWith(
        isSlectedAll: value,
        slectedIds: value ? allTableIds : [],
        status: CallButtonCloseOutInactiveStatus.Edit,
      ),
    );
  }

  void onChangeSlectedIds(String id) {
    final currentIds = state.slectedIds ?? [];
    final updatedIds = currentIds.contains(id)
        ? currentIds.where((e) => e != id).toList()
        : [...currentIds, id];

    final totalTables = (state.tablesInactive ?? [])
        .where((table) => (table.id ?? '').isNotEmpty)
        .length;

    // Logic for isSlectedAll
    bool? isAllSelected;
    if (updatedIds.isEmpty) {
      isAllSelected = false;
    } else if (updatedIds.length == totalTables) {
      isAllSelected = true;
    } else {
      isAllSelected = null; // false Hoặc null nếu bạn muốn dùng tristate
    }

    emit(
      state.copyWith(
        slectedIds: updatedIds,
        isSlectedAll: isAllSelected,
        status: CallButtonCloseOutInactiveStatus.Edit,
      ),
    );
    AppLog.e(
        'updatedIds == ${updatedIds.length} /// isSlectedAll = ${state.isSlectedAll} //// $isAllSelected');
  }

  void onUpdateTablesInactive(List<CallButtonTable>? tables,
      {bool isResetAll = false}) {
    final currentIds = state.slectedIds ?? [];

    final totalTables = (state.tablesInactive ?? [])
        .where((table) => (table.id ?? '').isNotEmpty)
        .length;

    // Logic for isSlectedAll
    bool? isAllSelected;
    if (currentIds.isEmpty) {
      isAllSelected = false;
    } else if (currentIds.length == totalTables) {
      isAllSelected = true;
    } else {
      isAllSelected = null; // false Hoặc null nếu bạn muốn dùng tristate
    }

    if (isResetAll) {
      emit(
        state.copyWith(
          tablesInactive: tables,
          slectedIds: [],
          isSlectedAll: false,
          status: CallButtonCloseOutInactiveStatus.Edit,
        ),
      );
    } else {
      emit(
        state.copyWith(
          tablesInactive: tables,
          isSlectedAll: isAllSelected,
          status: CallButtonCloseOutInactiveStatus.Edit,
        ),
      );
    }
  }

  // Future<List<CloseOutInactive>?> onCloseOutInactiveSelected(Map<String, dynamic> body) async {
  //   emit(state.copyWith(status: CallButtonCloseOutInactiveStatus.Loading));
  //   try {
  //     final response = await _service.onCloseOutInactiveTableSelected(body);
  //     if (response != null) {
  //       AppLog.e('close out selected = ${jsonEncode(response)}');
  //       emit(
  //         state.copyWith(
  //           data: response,
  //           isSlectedAll: false,
  //           slectedIds: [],
  //           tablesInactive: [],
  //           status: CallButtonCloseOutInactiveStatus.Success,
  //         ),
  //       );
  //     }
  //   } on AppErrorResponse catch (e) {
  //     emit(
  //       state.copyWith(
  //         status: CallButtonCloseOutInactiveStatus.Error,
  //         errorMsg: e.message,
  //       ),
  //     );
  //   }
  //   return null;
  // }
  
  Future<List<CloseOutInactive>?> onCloseOutInactiveSelected(Map<String, dynamic> body) async {
    emit(state.copyWith(status: CallButtonCloseOutInactiveStatus.Loading));
    try {
      final response = await _service.onCloseOutInactiveTableSelected(body);
      if (response != null) {
        AppLog.e('close out selected = ${jsonEncode(response)}');
        
        // Lấy danh sách ID từ response
        final responseIds = response.map((item) => item.id).where((id) => id != null).toSet();
        
        // Lọc ra những table không có ID trong response (tức là giữ lại những table không bị close out)
        final currentTables = state.tablesInactive ?? [];
        final updatedTables = currentTables
            .where((table) => !responseIds.contains(table.id))
            .toList();
        
        AppLog.e('Response IDs: $responseIds');
        AppLog.e('Tables before: ${currentTables.length}');
        AppLog.e('Tables after: ${updatedTables.length}');
        
        emit(
          state.copyWith(
            data: response,
            tablesInactive: updatedTables, // Cập nhật danh sách table đã lọc
            isSlectedAll: false,
            slectedIds: [],
            status: CallButtonCloseOutInactiveStatus.Success,
          ),
        );
      }
    } on AppErrorResponse catch (e) {
      emit(
        state.copyWith(
          status: CallButtonCloseOutInactiveStatus.Error,
          errorMsg: e.message,
        ),
      );
    }
    return null;
  }

}
