part of 'call-button-close-out-inactive.cubit.dart';

enum CallButtonCloseOutInactiveStatus { Initial, Edit, Progress, Error, Loading, Success }

class CallButtonCloseOutInactiveState {
  final CallButtonCloseOutInactiveStatus status;
  final List<CloseOutInactive>? data;
  final List<CallButtonTable>? tablesInactive;
  final List<String>? slectedIds;
  final bool? isSlectedAll;
  final String? errorMsg;

  const CallButtonCloseOutInactiveState({
    this.status = CallButtonCloseOutInactiveStatus.Initial,
    this.data,
    this.tablesInactive,
    this.slectedIds,
    this.isSlectedAll,
    this.errorMsg,
  });

  CallButtonCloseOutInactiveState copyWith({
    CallButtonCloseOutInactiveStatus? status,
    List<CloseOutInactive>? data,
    List<CallButtonTable>? tablesInactive,
    List<String>? slectedIds,
    bool? isSlectedAll,
    String? errorMsg,
  }) {
    return CallButtonCloseOutInactiveState(
      status: status ?? this.status,
      data: data ?? this.data,
      tablesInactive: tablesInactive ?? this.tablesInactive,
      slectedIds: slectedIds ?? this.slectedIds,
      isSlectedAll: isSlectedAll,
      errorMsg: errorMsg ?? this.errorMsg,
    );
  }

  @override
  String toString() {
    return 'CallButtonCloseOutInactiveState  $status';
  }
}
