import 'package:json_annotation/json_annotation.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/models/section-table-detail.model.dart';

part 'call-button-sections.model.g.dart';

@JsonSerializable()
class CallButtonSections {
  CallButtonSections({
    this.id,
    this.name,
    this.icon,
    this.index,
    this.tables,
    this.createdAt,
  });

  @JsonKey(
    name: 'id',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? id;

  @Json<PERSON>ey(
    name: 'name',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'icon')
  dynamic icon;

  @JsonKey(name: 'index')
  dynamic index;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'tables')
  List<CallButtonTable>? tables;

  @Json<PERSON>ey(name: 'createdAt')
  DateTime? createdAt;

  factory CallButtonSections.from<PERSON>son(Map<String, dynamic> data) => _$CallButtonSectionsFromJson(data);
  Map<String?, dynamic> toJson() => _$CallButtonSectionsToJson(this);
}

@JsonSerializable()
class CallButtonTable {
  CallButtonTable({
    this.id,
    this.name,
    this.icon,
    this.index,
    this.callButtonId,
    this.requests,
  });

  @JsonKey(
    name: 'id',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? id;

  @JsonKey(
    name: 'name',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? name;

  @JsonKey(name: 'icon')
  dynamic icon;

  @JsonKey(name: 'index')
  int? index;

  @JsonKey(
    name: 'callButtonId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? callButtonId;

  @JsonKey(name: 'requests')
  List<CallButtonRequest>? requests;

  @JsonKey(name: 'isInactive')
  bool? isInactive;

  @JsonKey(name: 'checkins')
  List<Checkin>? checkins;

  factory CallButtonTable.fromJson(Map<String, dynamic> data) => _$CallButtonTableFromJson(data);
  Map<String?, dynamic> toJson() => _$CallButtonTableToJson(this);
}

@JsonSerializable()
class CallButtonRequest {
  CallButtonRequest({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.isDone,
    this.userId,
    this.metadata,
    this.notes,
    this.user,
    this.service,
    this.isCustomRequest,
    this.customRequestName,
    this.customRequestIcon,
    this.customRequestType,
    this.products,
  });

  @JsonKey(
    name: 'id',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? id;

  @JsonKey(name: 'createdAt')
  DateTime? createdAt;

  @JsonKey(name: 'updatedAt')
  DateTime? updatedAt;

  @JsonKey(name: 'isDone')
  bool? isDone;

  @JsonKey(name: 'userId')
  dynamic userId;

  @JsonKey(name: 'metadata')
  dynamic metadata;

  @JsonKey(
    name: 'notes',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? notes;

  @JsonKey(name: 'user')
  dynamic user;

  @JsonKey(name: 'service')
  Service? service;

  @JsonKey(name: 'isCustomRequest')
  bool? isCustomRequest;

  @JsonKey(
    name: 'customRequestName',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? customRequestName;

  @JsonKey(
    name: 'customRequestIcon',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? customRequestIcon;

  @JsonKey(
    name: 'customRequestType',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? customRequestType;

  @JsonKey(name: 'products')
  CallButtonRequestProduct? products;

  factory CallButtonRequest.fromJson(Map<String, dynamic> data) => _$CallButtonRequestFromJson(data);
  Map<String?, dynamic> toJson() => _$CallButtonRequestToJson(this);
}

@JsonSerializable()
class LastRequestNotAck {
  @JsonKey(name: 'date')
  DateTime? date;

  LastRequestNotAck({
    this.date,
  });

  LastRequestNotAck copyWith({
    DateTime? date,
  }) =>
      LastRequestNotAck(
        date: date ?? this.date,
      );

  factory LastRequestNotAck.fromJson(Map<String, dynamic> data) => _$LastRequestNotAckFromJson(data);
  Map<String?, dynamic> toJson() => _$LastRequestNotAckToJson(this);
}

@JsonSerializable()
class Checkin {
  Checkin({
    this.id,
    this.userId,
    this.userPhone,
    this.userDisplayName,
    this.isDoneAdditionalPoints,
    this.user,
  });

  @JsonKey(
    name: 'id',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? id;

  @JsonKey(
    name: 'userId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? userId;

  @JsonKey(
    name: 'userPhone',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? userPhone;

  @JsonKey(
    name: 'userDisplayName',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? userDisplayName;

  @JsonKey(name: 'isDoneAdditionalPoints',)
  bool? isDoneAdditionalPoints;

  @JsonKey(name: 'user')
  UserCheckin? user;

  Checkin copyWith({
    String? id,
    String? userId,
    String? userPhone,
    String? userDisplayName,
    bool? isDoneAdditionalPoints,
    UserCheckin? user,
  }) =>
      Checkin(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        userPhone: userPhone ?? this.userPhone,
        userDisplayName: userDisplayName ?? this.userDisplayName,
        isDoneAdditionalPoints: isDoneAdditionalPoints ?? this.isDoneAdditionalPoints,
        user: user ?? this.user,
      );

  factory Checkin.fromJson(Map<String, dynamic> data) => _$CheckinFromJson(data);
  Map<String?, dynamic> toJson() => _$CheckinToJson(this);
}

@JsonSerializable()
class UserCheckin {
  UserCheckin({
    this.displayName,
    this.phone,
    this.email,
  });

  @JsonKey(
    name: 'displayName',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? displayName;

  @JsonKey(
    name: 'phone',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? phone;

  @JsonKey(
    name: 'email',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? email;

  @JsonKey(name: 'isActive')
  bool? isActive;

  factory UserCheckin.fromJson(Map<String, dynamic> data) => _$UserCheckinFromJson(data);
  Map<String?, dynamic> toJson() => _$UserCheckinToJson(this);
}

// @JsonSerializable()
// class Service {
//   Service({
//     this.id,
//     this.name,
//     this.index,
//     this.icon,
//     this.type,
//     this.tyleKey,
//     this.typeValue,
//   });

//   @JsonKey(
//     name: 'id',
//     fromJson: ModelTypeCast.data2String,
//     toJson: ModelTypeCast.data2String,
//   )
//   String? id;

//   @JsonKey(
//     name: 'name',
//     fromJson: ModelTypeCast.data2String,
//     toJson: ModelTypeCast.data2String,
//   )
//   String? name;

//   @JsonKey(
//     name: 'index',
//     fromJson: ModelTypeCast.data2Int,
//     toJson: ModelTypeCast.data2Int,
//   )
//   int? index;

//   @JsonKey(
//     name: 'icon',
//     fromJson: ModelTypeCast.data2String,
//     toJson: ModelTypeCast.data2String,
//   )
//   String? icon;

//   @JsonKey(
//     name: 'type',
//     fromJson: ModelTypeCast.data2String,
//     toJson: ModelTypeCast.data2String,
//   )
//   String? type;

//   @JsonKey(name: 'tyleKey')
//   dynamic tyleKey;

//   @JsonKey(name: 'typeValue')
//   dynamic typeValue;

//   factory Service.fromJson(Map<String, dynamic> data) => _$ServiceFromJson(data);
//   Map<String?, dynamic> toJson() => _$ServiceToJson(this);
// }

@JsonSerializable()
class CallButtonRequestProduct {
    CallButtonRequestProduct({
        this.id,
        this.name,
        this.notes,
        this.price,
        this.total,
        this.currency,
        this.quantity,
        this.modifiers,
    });
    
    @JsonKey(
      name: 'id',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? id;

    @JsonKey(
      name: 'name',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? name;
    
    @JsonKey(
      name: 'notes',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? notes;

    @JsonKey(name: 'price')
    double? price;

    @JsonKey(name: 'total')
    double? total;

    @JsonKey(
      name: 'currency',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? currency;

    @JsonKey(
      name: 'quantity',
      fromJson: ModelTypeCast.data2Int,
      toJson: ModelTypeCast.data2Int,
    )
    int? quantity;

    @JsonKey(name: 'modifiers')
    List<CBModifier>? modifiers;

  factory CallButtonRequestProduct.fromJson(Map<String, dynamic> data) => _$CallButtonRequestProductFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonRequestProductToJson(this);
}

@JsonSerializable()
class CBModifier {
    CBModifier({
        this.id,
        this.price,
        this.total,
        this.optionName,
        this.modifierName,
        this.options,
    });

    @JsonKey(
      name: 'id',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? id;

    @JsonKey(name: 'price')
    double? price;

    @JsonKey(name: 'total')
    double? total;

    @JsonKey(
      name: 'optionName',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? optionName;

    @JsonKey(
      name: 'modifierName',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? modifierName;

    @JsonKey(name: 'options')
    List<CBModifierOption>? options;

  factory CBModifier.fromJson(Map<String, dynamic> data) => _$CBModifierFromJson(data);
  Map<String, dynamic> toJson() => _$CBModifierToJson(this);
}

@JsonSerializable()
class CBModifierOption {
    CBModifierOption({
        this.id,
        this.name,
        this.price,
        this.isActive,
        this.metadata,
        this.isDefault,
        this.currencyCode,
        this.isUnavailable,
        this.unAvailableUntil,
    });

    @JsonKey(
      name: 'id',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? id;

    @JsonKey(
      name: 'name',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? name;

    @JsonKey(name: 'price')
    double? price;

    @JsonKey(name: 'isActive')
    bool? isActive;

    @JsonKey(name: 'metadata')
    dynamic metadata;

    @JsonKey(name: 'isDefault')
    bool? isDefault;

    @JsonKey(
      name: 'currencyCode',
      fromJson: ModelTypeCast.data2String,
      toJson: ModelTypeCast.data2String,
    )
    String? currencyCode;

    @JsonKey(name: 'isUnavailable')
    bool? isUnavailable;

    @JsonKey(name: 'unAvailableUntil')
    DateTime? unAvailableUntil;


  factory CBModifierOption.fromJson(Map<String, dynamic> data) => _$CBModifierOptionFromJson(data);
  Map<String, dynamic> toJson() => _$CBModifierOptionToJson(this);
}
