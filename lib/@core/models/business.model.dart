import 'package:json_annotation/json_annotation.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/models/models.dart';

part 'business.model.g.dart';

@JsonSerializable()
class Business {
  @Json<PERSON>ey(
    name: 'id',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? id;

  @Json<PERSON>ey(
    name: 'createdAt',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: 'updatedAt',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? updatedAt;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: 'name',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? name;

  @JsonKey(
    name: 'logo',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? logo;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: 'phone',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? phone;

  @JsonKey(
    name: 'email',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? email;

  @JsonKey(
    name: 'timeZone',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? timeZone;

  @JsonKey(
    name: 'currency',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? currency;

  @JsonKey(
    name: 'currencyCode',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? currencyCode;

  @JsonKey(
    name: 'type',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? type;

  @JsonKey(
    name: 'addressId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? addressId;

  @JsonKey(
    name: 'slug',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? slug;

  @JsonKey(
    name: 'userId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? userId;

  @JsonKey(name: 'address')
  final AddressUser? address;

  @JsonKey(name: 'avatar')
  final Avatar? avatar;

  @JsonKey(
    name: 'avatarId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? avatarId;

  @JsonKey(
    name: 'authorId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? authorId;

  @JsonKey(
    name: 'role',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? role;

  @JsonKey(
    name: 'language',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? language;

  @JsonKey(
    name: 'referralProgram',
  )
  ReferralProgram? referralProgram;

  @JsonKey(
    name: 'enableReferralProgram',
  )
  bool? enableReferralProgram;

  @JsonKey(
    name: 'currentPlan',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? currentPlan;

  @JsonKey(
    name: 'currentPlanInterval',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? currentPlanInterval;

  @JsonKey(
    name: 'currentPlanStatus',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? currentPlanStatus;

  @JsonKey(
    name: 'planMaxCustomers',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? planMaxCustomers;

  @JsonKey(
    name: 'totalActiveCustomers',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? totalActiveCustomers;

  @JsonKey(
    name: 'businessSubscriptionId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? businessSubscriptionId;

  @JsonKey(
    name: 'credits',
  )
  final double? credits;

  @JsonKey(
    name: 'getStartedCodeFromLinks',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? getStartedCodeFromLinks;

  @JsonKey(
    name: 'showScanPrintersManual',
  )
  bool? showScanPrintersManual;

  @JsonKey(
    name: 'acceptCustomerPayment',
  )
  bool? acceptCustomerPayment;

  @JsonKey(
    name: 'acceptCustomerClaimsPoint',
  )
  bool? acceptCustomerClaimsPoint;

  @JsonKey(
    name: 'enableSoundNewPointClaim',
  )
  bool? enableSoundNewPointClaim;

  @JsonKey(
    name: 'soundNewPointClaimsRepeat',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  final int? soundNewPointClaimsRepeat;

  @JsonKey(
    name: 'soundNewPointClaimName',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  final int? soundNewPointClaimName;

  @JsonKey(
    name: 'isDemoBusiness',
  )
  final bool? isDemoBusiness;

  @JsonKey(
    name: 'enableCallButton',
  )
  bool? enableCallButton;

  @JsonKey(
    name: 'enableCallButtonSound',
  )
  bool? enableCallButtonSound;

  @JsonKey(
    name: 'callButtonSoundRepeat',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  final int? callButtonSoundRepeat;

  @JsonKey(
    name: 'callButtonSoundName',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  final int? callButtonSoundName;

  @JsonKey(
    name: 'callButtonThankYouCustomize',
  )
  final CallButtonThankYouCustomize? callButtonThankYouCustomize;

  @JsonKey(
    name: 'businessRole',
  )
  final BusinessRole? businessRole;

  @JsonKey(
    name: 'enableHMC',
  )
  bool? enableHMC;

  @JsonKey(
    name: 'enableCallButtonHMC',
  )
  bool? enableCallButtonHMC;

  @JsonKey(
    name: 'enableCallButtonOrdering',
  )
  bool? enableCallButtonOrdering;

  @JsonKey(
    name: 'hmcData',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? hmcData;

  @JsonKey(
    name: 'callButtonHMC',
  )
  CallButtonHmc? callButtonHMC;

  @JsonKey(
    name: 'callButtonHMCCart',
  )
  CallButtonHmcCart? callButtonHMCCart;

  @JsonKey(
    name: 'callButtonHMCGetHelp',
  )
  CallButtonHMCGetHelp? callButtonHMCGetHelp;

  @JsonKey(
    name: 'callButtonHMCAppearance',
  )
  CallButtonHMCAppearance? callButtonHMCAppearance;

  @JsonKey(
    name: 'callButtonThankYouButtons',
  )
  List<CallButtonThankYouButton>? callButtonThankYouButtons;

  @JsonKey(
    name: 'callButtonCheckin',
  )
  CallButtonCheckin? callButtonCheckin;

  @JsonKey(
    name: 'modulesStatus',
  )
  ModulesStatus? modulesStatus;

  @JsonKey(
    name: 'features',
  )
  List<Feature>? features;

  @JsonKey(
    name: 'orderAutoConfirm',
  )
  bool? orderAutoConfirm;

  @JsonKey(
    name: 'orderSoundRepeat',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? orderSoundRepeat;

  @JsonKey(
    name: 'orderSoundName',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? orderSoundName;

  @JsonKey(name: 'orderAllowOnlineOrdering')
  bool? orderAllowOnlineOrdering;

  @JsonKey(
    name: 'orderOperationStatus',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? orderOperationStatus;

  @JsonKey(name: 'orderOperationStatusUntil')
  DateTime? orderOperationStatusUntil;

  @JsonKey(
    name: 'orderAutoReady',
  )
  bool? orderAutoReady;

  @JsonKey(
    name: 'orderAutoComplete',
  )
  bool? orderAutoComplete;

  @JsonKey(
    name: 'orderAllowSendSmsOrderStatus',
  )
  bool? orderAllowSendSmsOrderStatus;

  @JsonKey(
    name: 'orderAutoConfirmAfter',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? orderAutoConfirmAfter;

  @JsonKey(
    name: 'orderAutoCompleteAfter',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? orderAutoCompleteAfter;

  @JsonKey(
    name: 'orderAutoReadyAfter',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? orderAutoReadyAfter;

  @JsonKey(
    name: 'googlePlaceId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? googlePlaceId;

  @JsonKey(name: 'callButtonAllowReviewGoogle')
  bool? callButtonAllowReviewGoogle;

  @JsonKey(name: 'orderAllowPayAtStore')
  bool? orderAllowPayAtStore;

  @JsonKey(name: 'enableTargetOffers')
  bool? enableTargetOffers;

  @JsonKey(name: 'enableOrderDeliveryFeature')
  bool? enableOrderDeliveryFeature;

  @JsonKey(name: 'orderAllowDelivery')
  bool? orderAllowDelivery;

  @JsonKey(name: 'enableTargetOffersIntegrate')
  bool? enableTargetOffersIntegrate;

  @JsonKey(name: 'isShowBubbleSupport')
  bool? isShowBubbleSupport;

  @JsonKey(name: 'orderDeliveryFeeSupport')
  OrderDeliveryFeeSupport? orderDeliveryFeeSupport;

  @JsonKey(name: 'isNewHomePage')
  bool? isNewHomePage;

  @JsonKey(name: 'isAllowFlexibleHomePage')
  bool? isAllowFlexibleHomePage;

  @JsonKey(
    name: 'newHomePageDefaultPage',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? newHomePageDefaultPage;
  
  @JsonKey(name: 'cbTableStatusSettings')
  CallButtonTableStatusSettings? cbTableStatusSettings;

  Business({
    this.newHomePageDefaultPage,
    this.isAllowFlexibleHomePage,
    this.isNewHomePage,
    this.isShowBubbleSupport,
    this.orderDeliveryFeeSupport,
    this.enableTargetOffersIntegrate,
    this.orderAllowDelivery,
    this.enableOrderDeliveryFeature,
    this.enableTargetOffers,
    this.orderAllowPayAtStore,
    this.orderAutoComplete,
    this.callButtonAllowReviewGoogle,
    this.orderAutoConfirmAfter,
    this.orderAutoCompleteAfter,
    this.orderAutoReadyAfter,
    this.orderAllowSendSmsOrderStatus,
    this.orderAutoReady,
    this.callButtonThankYouButtons,
    this.businessRole,
    this.callButtonThankYouCustomize,
    this.isDemoBusiness,
    this.soundNewPointClaimName,
    this.soundNewPointClaimsRepeat,
    this.enableSoundNewPointClaim,
    this.acceptCustomerClaimsPoint,
    this.showScanPrintersManual,
    this.getStartedCodeFromLinks,
    this.credits,
    this.currentPlan,
    this.currentPlanInterval,
    this.planMaxCustomers,
    this.totalActiveCustomers,
    this.currentPlanStatus,
    this.businessSubscriptionId,
    this.referralProgram,
    this.enableReferralProgram,
    this.language,
    this.role,
    this.id,
    this.createdAt,
    this.updatedAt,
    this.name,
    this.logo,
    this.phone,
    this.email,
    this.timeZone,
    this.currency,
    this.type,
    this.addressId,
    this.slug,
    this.userId,
    this.address,
    this.avatar,
    this.avatarId,
    this.authorId,
    this.acceptCustomerPayment,
    this.enableCallButton,
    this.enableCallButtonSound,
    this.callButtonSoundRepeat,
    this.callButtonSoundName,
    this.enableHMC,
    this.enableCallButtonHMC,
    this.enableCallButtonOrdering,
    this.hmcData,
    this.callButtonHMC,
    this.callButtonHMCCart,
    this.callButtonHMCGetHelp,
    this.callButtonHMCAppearance,
    this.callButtonCheckin,
    this.modulesStatus,
    this.features,
    this.orderAutoConfirm,
    this.orderSoundRepeat,
    this.orderSoundName,
    this.orderAllowOnlineOrdering,
    this.orderOperationStatus,
    this.orderOperationStatusUntil,
    this.currencyCode,
    this.cbTableStatusSettings,
  });

  Business copyWith({
    List<CallButtonThankYouButton>? callButtonThankYouButtons,
    bool? callButtonAllowReviewGoogle,
    bool? isDemoBusiness,
    bool? showScanPrintersManual,
    double? credits,
    String? currentPlan,
    String? currentPlanInterval,
    String? currentPlanStatus,
    String? planMaxCustomers,
    String? totalActiveCustomers,
    String? businessSubscriptionId,
    String? language,
    String? id,
    String? createdAt,
    String? updatedAt,
    String? name,
    String? logo,
    String? phone,
    String? email,
    String? timeZone,
    String? currency,
    String? type,
    String? addressId,
    String? slug,
    String? userId,
    AddressUser? address,
    Avatar? avatar,
    String? avatarId,
    String? authorId,
    String? role,
    String? getStartedCodeFromLinks,
    ReferralProgram? referralProgram,
    bool? enableReferralProgram,
    bool? acceptCustomerPayment,
    bool? acceptCustomerClaimsPoint,
    bool? enableSoundNewPointClaim,
    int? soundNewPointClaimsRepeat,
    int? soundNewPointClaimName,
    bool? enableCallButton,
    bool? enableCallButtonSound,
    int? callButtonSoundRepeat,
    int? callButtonSoundName,
    CallButtonThankYouCustomize? callButtonThankYouCustomize,
    BusinessRole? businessRole,
    bool? enableHMC,
    bool? enableCallButtonHMC,
    bool? enableCallButtonOrdering,
    String? hmcData,
    CallButtonHmc? callButtonHMC,
    CallButtonHmcCart? callButtonHMCCart,
    CallButtonHMCGetHelp? callButtonHMCGetHelp,
    CallButtonHMCAppearance? callButtonHMCAppearance,
    CallButtonCheckin? callButtonCheckin,
    ModulesStatus? modulesStatus,
    List<Feature>? features,
    bool? orderAutoConfirm,
    int? orderSoundRepeat,
    int? orderSoundName,
    bool? orderAllowOnlineOrdering,
    String? orderOperationStatus,
    DateTime? orderOperationStatusUntil,
    bool? orderAllowSendSmsOrderStatus,
    bool? orderAutoReady,
    bool? orderAutoComplete,
    int? orderAutoConfirmAfter,
    int? orderAutoCompleteAfter,
    int? orderAutoReadyAfter,
    bool? orderAllowPayAtStore,
    bool? enableTargetOffers,
    bool? enableOrderDeliveryFeature,
    bool? orderAllowDelivery,
    String? currencyCode,
    bool? enableTargetOffersIntegrate,
    OrderDeliveryFeeSupport? orderDeliveryFeeSupport,
    bool? isShowBubbleSupport,
    bool? isNewHomePage,
    bool? isAllowFlexibleHomePage,
    String? newHomePageDefaultPage,
    CallButtonTableStatusSettings? cbTableStatusSettings,
  }) =>
      Business(
        newHomePageDefaultPage: newHomePageDefaultPage ?? this.newHomePageDefaultPage,
        isAllowFlexibleHomePage: isAllowFlexibleHomePage ?? this.isAllowFlexibleHomePage,
        isNewHomePage: isNewHomePage ?? this.isNewHomePage,
        isShowBubbleSupport: isShowBubbleSupport ?? this.isShowBubbleSupport,
        orderDeliveryFeeSupport: orderDeliveryFeeSupport ?? this.orderDeliveryFeeSupport,
        enableTargetOffersIntegrate: enableTargetOffersIntegrate ?? this.enableTargetOffersIntegrate,
        orderAllowDelivery: orderAllowDelivery ?? this.orderAllowDelivery,
        enableOrderDeliveryFeature: enableOrderDeliveryFeature ?? this.enableOrderDeliveryFeature,
        currencyCode: currencyCode ?? this.currencyCode,
        enableTargetOffers: enableTargetOffers ?? this.enableTargetOffers,
        orderAllowPayAtStore: orderAllowPayAtStore ?? this.orderAllowPayAtStore,
        orderAutoCompleteAfter: orderAutoCompleteAfter ?? this.orderAutoCompleteAfter,
        orderAutoComplete: orderAutoComplete ?? this.orderAutoComplete,
        callButtonAllowReviewGoogle: callButtonAllowReviewGoogle ?? this.callButtonAllowReviewGoogle,
        orderAutoReadyAfter: orderAutoReadyAfter ?? this.orderAutoReadyAfter,
        orderAutoConfirmAfter: orderAutoConfirmAfter ?? this.orderAutoConfirmAfter,
        orderAutoReady: orderAutoReady ?? this.orderAutoReady,
        orderAllowSendSmsOrderStatus: orderAllowSendSmsOrderStatus ?? this.orderAllowSendSmsOrderStatus,
        callButtonThankYouButtons: callButtonThankYouButtons ?? this.callButtonThankYouButtons,
        businessRole: businessRole ?? this.businessRole,
        callButtonThankYouCustomize: callButtonThankYouCustomize ?? this.callButtonThankYouCustomize,
        isDemoBusiness: isDemoBusiness ?? this.isDemoBusiness,
        soundNewPointClaimName: soundNewPointClaimName ?? this.soundNewPointClaimName,
        soundNewPointClaimsRepeat: soundNewPointClaimsRepeat ?? this.soundNewPointClaimsRepeat,
        enableSoundNewPointClaim: enableSoundNewPointClaim ?? this.enableSoundNewPointClaim,
        acceptCustomerClaimsPoint: acceptCustomerClaimsPoint ?? this.acceptCustomerClaimsPoint,
        showScanPrintersManual: showScanPrintersManual ?? this.showScanPrintersManual,
        getStartedCodeFromLinks: getStartedCodeFromLinks ?? this.getStartedCodeFromLinks,
        credits: credits ?? this.credits,
        currentPlan: currentPlan ?? this.currentPlan,
        currentPlanInterval: currentPlanInterval ?? this.currentPlanInterval,
        currentPlanStatus: currentPlanStatus ?? this.currentPlanStatus,
        planMaxCustomers: planMaxCustomers ?? this.planMaxCustomers,
        totalActiveCustomers: totalActiveCustomers ?? this.totalActiveCustomers,
        businessSubscriptionId: businessSubscriptionId ?? this.businessSubscriptionId,
        enableReferralProgram: enableReferralProgram ?? this.enableReferralProgram,
        referralProgram: referralProgram ?? this.referralProgram,
        language: language ?? this.language,
        role: role ?? this.role,
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        name: name ?? this.name,
        logo: logo ?? this.logo,
        phone: phone ?? this.phone,
        email: email ?? this.email,
        timeZone: timeZone ?? this.timeZone,
        currency: currency ?? this.currency,
        type: type ?? this.type,
        addressId: addressId ?? this.addressId,
        slug: slug ?? this.slug,
        userId: userId ?? this.userId,
        address: address ?? this.address,
        avatar: avatar ?? this.avatar,
        avatarId: avatarId ?? this.avatarId,
        authorId: authorId ?? this.authorId,
        acceptCustomerPayment: acceptCustomerPayment ?? this.acceptCustomerPayment,
        enableCallButton: enableCallButton ?? this.enableCallButton,
        enableCallButtonSound: enableCallButtonSound ?? this.enableCallButtonSound,
        callButtonSoundRepeat: callButtonSoundRepeat ?? this.callButtonSoundRepeat,
        callButtonSoundName: callButtonSoundName ?? this.callButtonSoundName,
        enableHMC: enableHMC ?? this.enableHMC,
        enableCallButtonHMC: enableCallButtonHMC ?? this.enableCallButtonHMC,
        enableCallButtonOrdering: enableCallButtonOrdering ?? this.enableCallButtonOrdering,
        hmcData: hmcData ?? this.hmcData,
        callButtonHMC: callButtonHMC ?? this.callButtonHMC,
        callButtonHMCCart: callButtonHMCCart ?? this.callButtonHMCCart,
        callButtonHMCGetHelp: callButtonHMCGetHelp ?? this.callButtonHMCGetHelp,
        callButtonHMCAppearance: callButtonHMCAppearance ?? this.callButtonHMCAppearance,
        callButtonCheckin: callButtonCheckin ?? this.callButtonCheckin,
        modulesStatus: modulesStatus ?? this.modulesStatus,
        features: features ?? this.features,
        orderAutoConfirm: orderAutoConfirm ?? this.orderAutoConfirm,
        orderSoundRepeat: orderSoundRepeat ?? this.orderSoundRepeat,
        orderSoundName: orderSoundName ?? this.orderSoundName,
        orderAllowOnlineOrdering: orderAllowOnlineOrdering ?? this.orderAllowOnlineOrdering,
        orderOperationStatus: orderOperationStatus ?? this.orderOperationStatus,
        orderOperationStatusUntil: orderOperationStatusUntil ?? this.orderOperationStatusUntil,
        cbTableStatusSettings: cbTableStatusSettings ?? this.cbTableStatusSettings,
      );

  factory Business.fromJson(Map<String, dynamic> data) => _$BusinessFromJson(data);
  Map<String, dynamic> toJson() => _$BusinessToJson(this);
}

@JsonSerializable()
class CallButtonTableStatusSettings {
  CallButtonTableStatusSettings({
    this.enableInProgress,
    this.inProgressHexColor,
    this.justSeatedHexColor,
    this.enableAttentionRequired,
    this.attentionRequiredHexColor,
    this.inProgressChangeAfterMinutes,
    this.attentionRequiredChangeAfterMinutes,
  });

  @JsonKey(name: 'enableInProgress')
  bool? enableInProgress;

  @JsonKey(
    name: 'inProgressHexColor',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? inProgressHexColor;

  @JsonKey(
    name: 'justSeatedHexColor',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? justSeatedHexColor;

  @JsonKey(
    name: 'attentionRequiredHexColor',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? attentionRequiredHexColor;

  @JsonKey(name: 'enableAttentionRequired')
  bool? enableAttentionRequired;

  @JsonKey(
    name: 'inProgressChangeAfterMinutes',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? inProgressChangeAfterMinutes;

  @JsonKey(
    name: 'attentionRequiredChangeAfterMinutes',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? attentionRequiredChangeAfterMinutes;

  factory CallButtonTableStatusSettings.fromJson(Map<String, dynamic> data) => _$CallButtonTableStatusSettingsFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonTableStatusSettingsToJson(this);
}

@JsonSerializable()
class AddressUser {
  AddressUser({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.street,
    this.city,
    this.state,
    this.country,
    this.lat,
    this.lng,
    this.zipCode,
  });

  @JsonKey(
    name: 'id',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? id;

  @JsonKey(name: 'createdAt')
  DateTime? createdAt;

  @JsonKey(name: 'updatedAt')
  DateTime? updatedAt;

  @JsonKey(
    name: 'street',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? street;

  @JsonKey(
    name: 'city',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? city;

  @JsonKey(
    name: 'state',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? state;

  @JsonKey(
    name: 'country',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? country;

  @JsonKey(name: 'lat')
  double? lat;

  @JsonKey(name: 'lng')
  double? lng;

  @JsonKey(
    name: 'zipCode',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? zipCode;

  factory AddressUser.fromJson(Map<String, dynamic> data) => _$AddressUserFromJson(data);
  Map<String, dynamic> toJson() => _$AddressUserToJson(this);
}

@JsonSerializable()
class Avatar {
  Avatar({
    this.id,
    this.publicId,
    this.file,
    this.mimetype,
    this.size,
    this.format,
    this.name,
    this.version,
    this.thumbnail,
    this.businessId,
    this.createdAt,
    this.updatedAt,
    this.authorId,
  });

  @JsonKey(
    name: 'id',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? id;

  @JsonKey(
    name: 'publicId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? publicId;

  @JsonKey(
    name: 'file',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? file;

  @JsonKey(
    name: 'mimetype',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? mimetype;

  @JsonKey(
    name: 'size',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? size;

  @JsonKey(name: 'format')
  dynamic format;

  @JsonKey(
    name: 'name',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? name;

  @JsonKey(name: 'version')
  dynamic version;

  @JsonKey(name: 'thumbnail')
  dynamic thumbnail;

  @JsonKey(
    name: 'businessId',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? businessId;

  @JsonKey(name: 'createdAt')
  DateTime? createdAt;

  @JsonKey(name: 'updatedAt')
  DateTime? updatedAt;

  @JsonKey(name: 'authorId')
  dynamic authorId;

  factory Avatar.fromJson(Map<String, dynamic> data) => _$AvatarFromJson(data);
  Map<String, dynamic> toJson() => _$AvatarToJson(this);
}

@JsonSerializable()
class CallButtonThankYouCustomize {
  @JsonKey(
    name: 'title',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? title;

  @JsonKey(
    name: 'message',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? message;

  @JsonKey(
    name: 'ctaButtonTitle',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? ctaButtonTitle;

  @JsonKey(
    name: 'ctaButtonLink',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? ctaButtonLink;

  CallButtonThankYouCustomize({
    this.title,
    this.message,
    this.ctaButtonTitle,
    this.ctaButtonLink,
  });

  CallButtonThankYouCustomize copyWith({
    String? title,
    String? message,
    String? ctaButtonTitle,
    String? ctaButtonLink,
  }) =>
      CallButtonThankYouCustomize(
        title: title ?? this.title,
        message: message ?? this.message,
        ctaButtonTitle: ctaButtonTitle ?? this.ctaButtonTitle,
        ctaButtonLink: ctaButtonLink ?? this.ctaButtonLink,
      );

  factory CallButtonThankYouCustomize.fromJson(Map<String, dynamic> data) => _$CallButtonThankYouCustomizeFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonThankYouCustomizeToJson(this);
}

@JsonSerializable()
class CallButtonHmc {
  CallButtonHmc({
    this.data,
    this.aiSendRequest,
    this.bubbleTextButton,
  });

  @JsonKey(
    name: 'data',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? data;

  @JsonKey(name: 'aiSendRequest')
  AiSendRequest? aiSendRequest;

  @JsonKey(
    name: 'bubbleTextButton',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? bubbleTextButton;

  factory CallButtonHmc.fromJson(Map<String, dynamic> data) => _$CallButtonHmcFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonHmcToJson(this);
}

@JsonSerializable()
class OrderDeliveryFeeSupport {
  OrderDeliveryFeeSupport({
    this.enabled,
    this.tiers,
  });

  @JsonKey(
    name: 'enabled',
  )
  bool? enabled;

  @JsonKey(name: 'tiers')
  List<Tier>? tiers;

  OrderDeliveryFeeSupport copyWith({List<Tier>? tiers, bool? enabled}) => OrderDeliveryFeeSupport(
        enabled: enabled ?? this.enabled,
        tiers: tiers ?? this.tiers,
      );

  factory OrderDeliveryFeeSupport.clone(OrderDeliveryFeeSupport? item) {
    return OrderDeliveryFeeSupport(
      enabled: item?.enabled,
      tiers: item?.tiers?.map((element) => Tier.clone(element)).toList(),
      // tiers: item?.tiers,
    );
  }

  factory OrderDeliveryFeeSupport.fromJson(Map<String, dynamic> data) => _$OrderDeliveryFeeSupportFromJson(data);
  Map<String, dynamic> toJson() => _$OrderDeliveryFeeSupportToJson(this);
}

@JsonSerializable()
class Tier {
  Tier({
    this.subTotal,
    this.type,
    this.amount,
    this.isFreeDelivery,
  });

  @JsonKey(name: 'subTotal')
  double? subTotal;

  @JsonKey(
    name: 'type',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? type;

  @JsonKey(
    name: 'amount',
  )
  double? amount;

  @JsonKey(
    name: 'isFreeDelivery',
  )
  bool? isFreeDelivery;

  Tier copyWith({
    double? subTotal,
    String? type,
    double? amount,
    bool? isFreeDelivery,
  }) =>
      Tier(
        subTotal: subTotal ?? this.subTotal,
        type: type ?? this.type,
        amount: amount ?? this.amount,
        isFreeDelivery: isFreeDelivery ?? this.isFreeDelivery,
      );

  factory Tier.clone(Tier? item) {
    return Tier(
      subTotal: item?.subTotal,
      type: item?.type,
      amount: item?.amount,
      isFreeDelivery: item?.isFreeDelivery,
    );
  }

  factory Tier.fromJson(Map<String, dynamic> data) => _$TierFromJson(data);
  Map<String, dynamic> toJson() => _$TierToJson(this);
}

@JsonSerializable()
class AiSendRequest {
  AiSendRequest({
    this.enabled,
    this.requestIcon,
    this.requestName,
  });

  @JsonKey(name: 'enabled')
  bool? enabled;

  @JsonKey(
    name: 'requestIcon',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? requestIcon;

  @JsonKey(
    name: 'requestName',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? requestName;

  factory AiSendRequest.fromJson(Map<String, dynamic> data) => _$AiSendRequestFromJson(data);
  Map<String, dynamic> toJson() => _$AiSendRequestToJson(this);
}

@JsonSerializable()
class CallButtonHmcCart {
  CallButtonHmcCart({
    this.enabled,
    this.requestIcon,
    this.requestName,
  });

  @JsonKey(name: 'enabled')
  bool? enabled;

  @JsonKey(
    name: 'requestIcon',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? requestIcon;

  @JsonKey(
    name: 'requestName',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? requestName;

  factory CallButtonHmcCart.fromJson(Map<String, dynamic> data) => _$CallButtonHmcCartFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonHmcCartToJson(this);
}

@JsonSerializable()
class CallButtonHMCGetHelp {
  CallButtonHMCGetHelp({
    this.enabled,
    this.requestIcon,
    this.requestName,
  });

  @JsonKey(name: 'enabled')
  bool? enabled;

  @JsonKey(
    name: 'requestName',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? requestName;

  @JsonKey(
    name: 'requestIcon',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? requestIcon;

  factory CallButtonHMCGetHelp.fromJson(Map<String, dynamic> data) => _$CallButtonHMCGetHelpFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonHMCGetHelpToJson(this);
}

@JsonSerializable()
class CallButtonHMCAppearance {
  CallButtonHMCAppearance({
    this.bubbleTextButton,
  });

  @JsonKey(
    name: 'bubbleTextButton',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? bubbleTextButton;

  factory CallButtonHMCAppearance.fromJson(Map<String, dynamic> data) => _$CallButtonHMCAppearanceFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonHMCAppearanceToJson(this);
}

@JsonSerializable()
class CallButtonThankYouButton {
  CallButtonThankYouButton({
    this.type,
    this.title,
    this.internalType,
    this.link,
  });

  @JsonKey(
    name: 'type',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? type;

  @JsonKey(
    name: 'title',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? title;

  @JsonKey(
    name: 'internalType',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? internalType;

  @JsonKey(
    name: 'link',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? link;

  factory CallButtonThankYouButton.fromJson(Map<String, dynamic> data) => _$CallButtonThankYouButtonFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonThankYouButtonToJson(this);
}

@JsonSerializable()
class CallButtonCheckin {
  CallButtonCheckin({
    this.enabled,
    this.requestName,
    this.requestLabel,
    this.allowEarnPoints,
    this.additionalPoints,
  });

  @JsonKey(name: 'enabled')
  bool? enabled;

  @JsonKey(
    name: 'requestName',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? requestName;

  @JsonKey(
    name: 'requestLabel',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  String? requestLabel;

  @JsonKey(name: 'allowEarnPoints')
  bool? allowEarnPoints;

  @JsonKey(
    name: 'additionalPoints',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? additionalPoints;

  factory CallButtonCheckin.fromJson(Map<String, dynamic> data) => _$CallButtonCheckinFromJson(data);
  Map<String, dynamic> toJson() => _$CallButtonCheckinToJson(this);
}

@JsonSerializable()
class ModulesStatus {
  ModulesStatus({
    this.modules,
    this.features,
    this.modulesReorder,
  });

  @JsonKey(name: 'modules')
  Modules? modules;

  @JsonKey(name: 'features')
  FeaturesModules? features;

  @JsonKey(name: 'modulesReorder')
  ModulesReorder? modulesReorder;

  factory ModulesStatus.fromJson(Map<String, dynamic> data) => _$ModulesStatusFromJson(data);
  Map<String, dynamic> toJson() => _$ModulesStatusToJson(this);
}

@JsonSerializable()
class FeaturesModules {
  FeaturesModules({
    this.helpMeChoose,
    this.pointClaims,
  });

  @JsonKey(name: 'helpMeChoose')
  bool? helpMeChoose;

  @JsonKey(name: 'pointClaims')
  bool? pointClaims;

  factory FeaturesModules.fromJson(Map<String, dynamic> data) => _$FeaturesModulesFromJson(data);
  Map<String, dynamic> toJson() => _$FeaturesModulesToJson(this);
}

@JsonSerializable()
class Modules {
  Modules({
    this.stickyPoints,
    this.stickyVouchers,
    this.callButton,
    this.additionalFeatures,
    this.general,
    this.orders,
    this.targetOffers,
  });

  @JsonKey(name: 'StickyPoints')
  bool? stickyPoints;

  @JsonKey(name: 'StickyVouchers')
  bool? stickyVouchers;

  @JsonKey(name: 'CallButton')
  bool? callButton;

  @JsonKey(name: 'AdditionalFeatures')
  bool? additionalFeatures;

  @JsonKey(name: 'General')
  bool? general;

  @JsonKey(name: 'Orders')
  bool? orders;

  @JsonKey(name: 'TargetOffers')
  bool? targetOffers;

  factory Modules.fromJson(Map<String, dynamic> data) => _$ModulesFromJson(data);
  Map<String, dynamic> toJson() => _$ModulesToJson(this);
}

@JsonSerializable()
class ModulesReorder {
  ModulesReorder({
    this.general,
    this.stickyVouchers,
    this.stickyPoints,
    this.callButton,
    this.additionalFeatures,
    this.targetOffers,
    this.orders,
  });

  @JsonKey(
    name: 'General',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? general;

  @JsonKey(
    name: 'StickyVouchers',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? stickyVouchers;

  @JsonKey(
    name: 'StickyPoints',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? stickyPoints;

  @JsonKey(
    name: 'CallButton',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? callButton;

  @JsonKey(
    name: 'AdditionalFeatures',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? additionalFeatures;

  @JsonKey(
    name: 'Orders',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? orders;

  @JsonKey(
    name: 'TargetOffers',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  int? targetOffers;

  factory ModulesReorder.fromJson(Map<String, dynamic> data) => _$ModulesReorderFromJson(data);
  Map<String, dynamic> toJson() => _$ModulesReorderToJson(this);

  Map<String, int> toMap() {
    return {
      'General': general ?? 0,
      'StickyVouchers': stickyVouchers ?? 0,
      'StickyPoints': stickyPoints ?? 0,
      'TargetOffers': targetOffers ?? 0,
      'Orders': orders ?? 0,
      'CallButton': callButton ?? 0,
      'AdditionalFeatures': additionalFeatures ?? 0,
    };
  }
}

@JsonSerializable()
class Feature {
  @JsonKey(
    name: 'module',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? module;

  @JsonKey(
    name: 'key',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? key;

  @JsonKey(
    name: 'index',
    fromJson: ModelTypeCast.data2Int,
    toJson: ModelTypeCast.data2Int,
  )
  final int? index;

  @JsonKey(
    name: 'customPlatforms',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? customPlatforms;

  @JsonKey(
    name: 'isEnabled',
  )
  final bool? isEnabled;

  @JsonKey(
    name: 'isProRequired',
  )
  final bool? isProRequired;

  @JsonKey(
    name: 'isEnterpriseRequired',
  )
  final bool? isEnterpriseRequired;

  @JsonKey(
    name: 'disabledType',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? disabledType;

  @JsonKey(
    name: 'title',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? title;

  @JsonKey(
    name: 'subtitle',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? subtitle;

  @JsonKey(
    name: 'description',
    fromJson: ModelTypeCast.data2String,
    toJson: ModelTypeCast.data2String,
  )
  final String? description;

  Feature({
    this.module,
    this.key,
    this.index,
    this.customPlatforms,
    this.isEnabled,
    this.isProRequired,
    this.isEnterpriseRequired,
    this.disabledType,
    this.title,
    this.subtitle,
    this.description,
  });

  Feature copyWith({
    String? module,
    String? key,
    int? index,
    String? customPlatforms,
    bool? isEnabled,
    bool? isProRequired,
    bool? isEnterpriseRequired,
    String? disabledType,
    String? title,
    String? subtitle,
    String? description,
  }) =>
      Feature(
        module: module ?? this.module,
        key: key ?? this.key,
        index: index ?? this.index,
        customPlatforms: customPlatforms ?? this.customPlatforms,
        isEnabled: isEnabled ?? this.isEnabled,
        isProRequired: isProRequired ?? this.isProRequired,
        isEnterpriseRequired: isEnterpriseRequired ?? this.isEnterpriseRequired,
        disabledType: disabledType ?? this.disabledType,
        title: title ?? this.title,
        subtitle: subtitle ?? this.subtitle,
        description: description ?? this.description,
      );
  factory Feature.fromJson(Map<String, dynamic> data) => _$FeatureFromJson(data);
  Map<String, dynamic> toJson() => _$FeatureToJson(this);
}
