import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PopoverMenuWidget extends StatelessWidget {
  final ValueChanged<PopoverModel> onChanged;
  final List<PopoverModel> menuItems;
  final Widget? child;
  final Widget? icon;
  final Color? textColor;
  final Color? iconColor;
  final double? textSize;
  final double? iconSize;
  final Offset? offset;
  final String? tooltip;
  final EdgeInsetsGeometry? padding;
  final FontWeight? fontWeight;
  const PopoverMenuWidget({
    Key? key,
    required this.onChanged,
    required this.menuItems,
    this.fontWeight = FontWeight.w500,
    this.child,
    this.icon,
    this.textColor,
    this.iconColor,
    this.textSize,
    this.iconSize,
    this.offset,
    this.tooltip,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.only(right: 8),
      child: Theme(
        data: Theme.of(context).copyWith(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          hoverColor: Colors.transparent,
        ),
        child: PopupMenuButton(
          icon: icon,
          tooltip: tooltip ?? '',
          onSelected: (PopoverModel value) {
            onChanged(value);
          },
          offset: offset ?? Offset(-8.0, AppBar().preferredSize.height - 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          itemBuilder: (context) => menuItems
              .map(
                (item) => PopupMenuItem<PopoverModel>(
                  value: item,
                  child: Row(
                    children: [
                      if (item.icon != null || item.svgIcon != null)
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: item.icon != null
                              ? Icon(
                                  item.icon,
                                  size: iconSize ?? 16,
                                  color: iconColor ?? Colors.black.withValues(alpha: .7),
                                )
                              : SvgPicture.asset(
                                  'assets/svgs/${item.svgIcon}.svg',
                                  width: iconSize,
                                  height: iconSize,
                                ),
                        ),
                      Text(
                        item.title,
                        style: TextStyle(
                          color: textColor ?? Colors.black.withValues(alpha: .7),
                          fontSize: textSize ?? 16,
                          fontWeight: fontWeight ?? FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
          child: child,
        ),
      ),
    );
  }
}

class PopoverModel {
  final String id;
  final String title;
  final IconData? icon;
  final String? svgIcon;

  PopoverModel({
    required this.id,
    required this.title,
    this.icon,
    this.svgIcon,
  });
}

// import 'package:flutter/material.dart';

// class PopoverMenuWidget extends StatelessWidget {
//   final ValueChanged<String> onChanged;
//   final List<String> menuItems;
//   final Widget? child;
//   final IconData? icon;
//   final Color? textColor;
//   final Color? iconColor;
//   final double? textSize;
//   final double? iconSize;
//   final Offset? offset;
//   final String? tooltip;
//   const PopoverMenuWidget({
//     Key? key,
//     required this.onChanged,
//     required this.menuItems,
//     this.child,
//     this.icon,
//     this.textColor,
//     this.iconColor,
//     this.textSize,
//     this.iconSize,
//     this.offset,
//     this.tooltip,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(right: 8),
//       child: Theme(
//         data: Theme.of(context).copyWith(
//           highlightColor: Colors.transparent,
//           splashColor: Colors.transparent,
//           hoverColor: Colors.transparent,
//         ),
//         child: PopupMenuButton(
//           tooltip: tooltip ?? '',
//           onSelected: (value) {
//             // ignore: noop_primitive_operations
//             onChanged(value);
//           },
//           offset: offset ?? Offset(-8.0, AppBar().preferredSize.height - 10),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(12),
//           ),
//           itemBuilder: (context) => menuItems
//               .map(
//                 (item) => PopupMenuItem<String>(
//                   value: item,
//                   child: Row(
//                     children: [
//                       if (icon != null)
//                         Padding(
//                           padding: const EdgeInsets.only(right: 8.0),
//                           child: Icon(
//                             icon,
//                             size: iconSize ?? 16,
//                             color: iconColor ?? Colors.black.withValues(alpha: .7),
//                           ),
//                         ),
//                       Text(
//                         item,
//                         style: TextStyle(
//                           color: textColor ?? Colors.black.withValues(alpha: .7),
//                           fontSize: textSize ?? 16,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               )
//               .toList(),
//           child: child,
//         ),
//       ),
//     );
//   }
// }

// class PopoverModel {
//   final String id;
//   final String title;
//   final IconData? icon;
//   final String? svgIcon;

//   PopoverModel({
//     required this.id,
//     required this.title,
//     this.icon,
//     this.svgIcon,
//   });
// }
