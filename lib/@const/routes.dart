// ignore_for_file: equal_keys_in_map

import 'package:flutter/material.dart';
import 'package:stickyqrbusiness/@core/enums.dart';
import 'package:stickyqrbusiness/app/app.dart';
import 'package:stickyqrbusiness/pages/account/account.dart';
import 'package:stickyqrbusiness/pages/activities/activities-page.dart';
import 'package:stickyqrbusiness/pages/announcements/announcements-page.dart';
import 'package:stickyqrbusiness/pages/announcements/widgets/@announcements-widgets.dart';
import 'package:stickyqrbusiness/pages/announcements/widgets/announcements-change-index.dart';
import 'package:stickyqrbusiness/pages/billing-payment/billing-payment.dart';
import 'package:stickyqrbusiness/pages/business-profile/profile-page.dart';
import 'package:stickyqrbusiness/pages/call-button/call-button-page.dart';
import 'package:stickyqrbusiness/pages/call-button/call-button-signage/widgets/cb-signage-counter-card-preview-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/@table-service.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/cb-help-me-choose/cb-help-me-choose-data-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/cb-help-me-choose/cb-help-me-choose-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/customize/call-button-setting-table-status-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/customize/call-button-thanks-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/features/call-button-checkin-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/features/call-button-review-us-google.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/service/cb-service-add-new-service-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/service/cb-service-reorder-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/service/scan-qr-code/cb-scan-qr-page.dart';
import 'package:stickyqrbusiness/pages/call-button/widgets/table/cb-table-reorder-page.dart';
import 'package:stickyqrbusiness/pages/change-plan/change-plan-page.dart';
import 'package:stickyqrbusiness/pages/change-plan/widgets/inapp-plan-page.dart';
import 'package:stickyqrbusiness/pages/chatbot-ai/chatbot.dart';
import 'package:stickyqrbusiness/pages/close-account/close-account-page.dart';
import 'package:stickyqrbusiness/pages/codes-scan/codes-scan-page.dart';
import 'package:stickyqrbusiness/pages/customer-payment/customer-payment-page.dart';
import 'package:stickyqrbusiness/pages/customers/customers-page.dart';
import 'package:stickyqrbusiness/pages/customers/widgets/@customers-widget.dart';
import 'package:stickyqrbusiness/pages/feature-tour/feature-tour-page.dart';
import 'package:stickyqrbusiness/pages/features/features-page.dart';
import 'package:stickyqrbusiness/pages/features/widgets/feature-point-claims.dart';
import 'package:stickyqrbusiness/pages/first-screen/first-screen-page.dart';
import 'package:stickyqrbusiness/pages/forgot/forgot-page.dart';
import 'package:stickyqrbusiness/pages/help-me-choose/help-me-choose-page.dart';
import 'package:stickyqrbusiness/pages/help-me-choose/widgets/help-me-choose-data-page.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/@home-widgets.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/multiple-home-reorder/multi-home-add-widget.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/multiple-home-reorder/multi-home-list-setting-page.dart';
import 'package:stickyqrbusiness/pages/label-template/labels-template-page.dart';
import 'package:stickyqrbusiness/pages/label-template/widgets/@label-template-widgets.dart';
import 'package:stickyqrbusiness/pages/login/login-not-password-page.dart';
import 'package:stickyqrbusiness/pages/login/login-with-password-by-customer-page.dart';
import 'package:stickyqrbusiness/pages/login/login-with-password-page.dart';
import 'package:stickyqrbusiness/pages/offers/vouchers-page.dart';
import 'package:stickyqrbusiness/pages/offers/widgets/@offers.dart';
import 'package:stickyqrbusiness/pages/order-history/order-history-detail-page.dart';
import 'package:stickyqrbusiness/pages/order-history/order-history-page.dart';
import 'package:stickyqrbusiness/pages/ordering-management-product/categories-management-page.dart';
import 'package:stickyqrbusiness/pages/ordering-management-product/product-detail-page.dart';
import 'package:stickyqrbusiness/pages/orders-active/orders-active-notification-page.dart';
import 'package:stickyqrbusiness/pages/orders-active/orders-active-page.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/new-delivery/delivery-date-time.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/new-delivery/new-delivery-page.dart';
import 'package:stickyqrbusiness/pages/pending-approvals/pending-approvals-page.dart';
import 'package:stickyqrbusiness/pages/printers/printer-add-page.dart';
import 'package:stickyqrbusiness/pages/printers/printers-page.dart';
import 'package:stickyqrbusiness/pages/redeem-point/redeem-page.dart';
import 'package:stickyqrbusiness/pages/referral/referral-page.dart';
import 'package:stickyqrbusiness/pages/rewards/rewards-page.dart';
import 'package:stickyqrbusiness/pages/rewards/widgets/@rewards-widget.dart';
import 'package:stickyqrbusiness/pages/role-permissions/@role-permissions-widgets.dart';
import 'package:stickyqrbusiness/pages/scan-qr-code/scan-qr-page.dart';
import 'package:stickyqrbusiness/pages/search-customer/search-customer.dart';
import 'package:stickyqrbusiness/pages/settings-new/settings-page-new.dart';
import 'package:stickyqrbusiness/pages/settings-new/widgets/@setting-widget.dart';
import 'package:stickyqrbusiness/pages/settings-new/widgets/call-button-manage-widget.dart';
import 'package:stickyqrbusiness/pages/settings-new/widgets/help-and-support-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/hardware-widget/hardware-printer-detail-page.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/hardware-widget/hardware-printer-page.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/hardware/hardware-page.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/integrations/integrations-order-delivery/integrations-order-delivery-page.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/integrations/integrations-page.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/integrations/integrations-sticky-points-page.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/order-alert-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/order-receipt-printer-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/order-store-hours/order-store-hours-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/order-store-status-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/settings-orders-page.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/inapp-plan-free-page.dart';
import 'package:stickyqrbusiness/pages/sign-up/sign-up-page.dart';
import 'package:stickyqrbusiness/pages/sign-up/sign-up-verification-code.dart';
import 'package:stickyqrbusiness/pages/signage/signage-page.dart';
import 'package:stickyqrbusiness/pages/signage/widgets/signage-counter-cards-point-claims/signage-point-claims-content.dart';
import 'package:stickyqrbusiness/pages/signage/widgets/signage-counter-cards-point-claims/signage-point-claims-preview.dart';
import 'package:stickyqrbusiness/pages/signage/widgets/signage-counter-cards/@signage-counter-cards.dart';
import 'package:stickyqrbusiness/pages/signage/widgets/signage-print-rewards/widgets/@widgets.dart';
import 'package:stickyqrbusiness/pages/splash.dart';
import 'package:stickyqrbusiness/pages/staffs/staffs-page.dart';
import 'package:stickyqrbusiness/pages/staffs/widgets/@staffs.dart';
import 'package:stickyqrbusiness/pages/statistics/statistics-page.dart';
import 'package:stickyqrbusiness/pages/target-offers-dashboard/to-dasboard-offer-performance-page.dart';
import 'package:stickyqrbusiness/pages/target-offers-dashboard/to-dasboard-page.dart';
import 'package:stickyqrbusiness/pages/target-offers-rfm-customer-segments/target-offers-rfm-customer-page.dart';
import 'package:stickyqrbusiness/pages/target-offers-rfm-customer-segments/target-offers-rfm-customer-segments-page.dart';
import 'package:stickyqrbusiness/pages/target-offers/target-offer-detail-page.dart';
import 'package:stickyqrbusiness/pages/target-offers/target-offers-page.dart';
import 'package:stickyqrbusiness/pages/test-responsive/test-responsive.dart';
import 'package:stickyqrbusiness/pages/test-responsive/widgets/test-detail.dart';

class AppRoutes {
  AppRoutes._();
  static const String splash = '/splash';
  static const String main = '/main';
  static const String testRespon = '/test-respon';
  static const String testResponDetail = '/test-respon-detail';
  static const String profile = '/profile';
  static const String home = '/home';
  static const String scanQRCode = '/scan-qr-code';
  static const String searchCustomner = '/search-customner';
  static const String redeemPoint = '/redeem-point';
  static const String printer = '/printer';
  static const String printerAddNew = '/printer-add-new';
  static const String forgot = '/forgot';
  static const String loginWithPassword = '/login_with_password';
  static const String loginNotPassword = '/login_not_password';
  static const String signUp = '/sign_up';
  static const String signUpVerificationCode = '/sign_up_verification_code';
  static const String rewards = '/rewards';
  static const String rewardDetail = '/rewards-detail';
  static const String rewardAddnew = '/rewards-add-new';
  static const String rewardChangeIndex = '/rewards-change-index';
  static const String settings = '/settings';
  static const String labelsTemplate = '/labels-template';
  static const String labelTemplateCustom = '/custom-label-template';
  static const String codeScanned = '/codeScanned';
  static const String staffs = '/staffs';
  static const String staffAdd = '/staff-add';
  static const String profileBusiness = '/profile-business';
  static const String activitiesPage = '/activities';
  static const String announcementPage = '/announcements';
  static const String announcementAddPage = '/announcement-add';
  static const String announcementDetailPage = '/announcement-detail';
  static const String announcementsChangeIndex = '/announcement-change-index';
  static const String account = '/account';
  static const String referral = '/referral';
  static const String cropImage = '/cropImage';
  static const String signageRewards = '/signageRewards';
  static const String signageSettingTemp = '/signageSettingTemp';
  static const String signage = '/signage';
  static const String signageCounter1 = '/signage-counter-1';
  // static const String signageRewardsTemplate1 = '/signage-reward-template-1';
  static const String signageRewardsTemplateReview = '/signage-reward-template-1';
  static const String signageCounterPreview = '/signage-counter-preview';
  static const String closeAccount = '/close-account';
  static const String usagePage = '/usage-page';
  static const String pendingApprovals = '/pending-approvals';
  static const String featureTourAnnoucement = '/feature-tour-announcement';
  static const String featureTourRewards = '/feature-tour-rewards';
  static const String featureTourReferral = '/feature-tour-referral';
  static const String customers = '/customers';
  static const String customerHistory = '/customer-history';
  static const String billingPayment = '/billing-payment';
  static const String onboardingSetUpRewards = '/onboarding-setup-rewards';
  static const String changePlan = '/change-plan';
  static const String firstScreen = '/first-screen';
  static const String inAppPlanPage = '/inapp-plan';
  static const String inAppPlanFreePage = '/inapp-plan-free';
  static const String offers = '/offers';
  static const String labelTemplateOfferCustom = '/custom-label-template-offer';
  static const String loginWithPasswordByCustomer = '/login-with-password-by-customer';
  static const String addOffer = '/offer-add';
  static const String detailOffer = '/offer-detail';
  static const String customerPayment = '/customer-payment';
  static const String signagePointClaims = '/signage-point-claims';
  static const String signagePointClaimsPreview = '/signage-point-claims-preview';
  static const String features = '/features';
  static const String featuresPointClaims = '/features-point-claims';
  static const String callButtonPage = '/call-button';
  static const String cbTable = '/call-button-table';
  static const String cbService = '/call-button-service';
  static const String cbScanQRCode = '/call-button-scanqrcode';
  static const String callButtonSignagePage = '/call-button-signage';
  static const String callButtonSettingSoundPage = '/call-button-setting-sound';
  static const String callButtonSettingThanksPage = '/call-button-setting-thanks';
  static const String callButtonServiceAddNewService = '/call-button-add-new-service';
  static const String callButtonTableReorderPage = '/call-button-table-reorder-page';
  static const String callButtonServiceReorderPage = '/call-button-service-reorder-page';
  static const String homeReorderWidgetSetting = '/home-reorder-widget-setting';
  static const String homeTemplate = '/home-multiple-template';
  static const String rolePermissionsPage = '/role-permission-page';
  static const String permissionsPage = '/permission-page';
  static const String callButtonHelpMeChoose = '/call-button-help-me-choose';
  static const String callButtonHelpMeChooseAddData = '/call-button-help-me-choose-add-data';
  static const String settingGeneral = '/setting-general';
  static const String callButtonManage = '/call-button-manage';
  static const String settingIntegrations = '/setting-integrations';
  static const String helpMeChoose = '/help-me-choose';
  static const String helpMeChooseAddData = '/help-me-choose-add-data';
  static const String callButtonSettingCheckin = '/call-button-setting-checkin';
  static const String helpAndSupport = '/help-and-support';
  static const String orderActivePage = 'ordering/active';
  // static const String orderActiveDetailPage = 'ordering/active/order-active-detail';
  static const String orderActiveNotifPage = 'ordering/active/order-active-notification';
  static const String managementProduct = 'ordering/management-product';
  static const String managementProductDetail = 'ordering/management-product/detail';
  static const String orderHistory = '/order-history';
  static const String settingsOrders = '/settings-orders';
  static const String settingsOrdersAlert = '/settings-orders-alert';
  static const String settingsOrdersPrinter = '/settings-orders-printer';
  static const String settingsOrdersStoreHours = '/settings-orders-hours';
  static const String updateStoreStatus = '/update-store-status';
  static const String integrationsPage = '/integrations';
  static const String integrationsPointsPage = '/integrations-points';
  static const String integrationsVouchersPage = '/integrations-vouchers';
  static const String annDescriptionEnhanceAI = '/ann-enhance-ai';
  static const String chatBot = '/chat-bot';
  static const String orderHistoryDetailPage = '/order-history-detail';
  static const String callButtonSettingReviewUsOnGooglePage = '/callbutton-setting-review-us-on-google';
  static const String callButtonSignageCounterCardPreviewPage = '/callbutton-signage-counter-card-preview-page';
  static const String targetOffersPage = '/target-offers-page';
  static const String targetOfferDetailPage = '/target-offer-detail-page';
  static const String targetOffersRFMCustomerSegmentsPage = '/target-offers-rfm-customer-segments-page';
  static const String targetOfferCustomerPage = '/target-offer-customer-page';
  static const String targetOfferDashboardPage = '/target-offer-dashboard-page';
  static const String targetOfferDashboardPerformancePage = '/target-offer-dashboard-performance-page';
  static const String integrationsDeliveryPage = '/integrations-order-delivery-page';
  static const String hardwarePrinterpage = '/hardware-printer-page';
  static const String hardwarePrinterDetailPage = '/hardware-printer-detail-page';
  static const String hardwarePage = '/hardware-page';
  static const String addNewDeliveryPage = '/add-new-delivery-page';
  static const String addNewDeliveryDateTimePage = '/ddd-new-delivery-date-time-page';
  static const String callButtonSettingTableStatusPage = '/call-button-setting-table-status-page';

  static final allRoutes = <String, WidgetBuilder>{
    main: (BuildContext _) => const StickyQRApp(),
    splash: (BuildContext _) => const SplashScreen(),
    testRespon: (BuildContext _) => const TestResponsive(),
    testResponDetail: (BuildContext _) => const DetailPage(),
    scanQRCode: (BuildContext _) => ScanQRCodePage(),
    searchCustomner: (BuildContext _) => const SearchCustomerPage(),
    redeemPoint: (BuildContext _) => const RedeemPointPage(),
    printer: (BuildContext _) => const PrintersPage(),
    printerAddNew: (BuildContext _) => const PrinterAddPage(),
    forgot: (BuildContext _) => const ForgotPage(),
    loginWithPassword: (BuildContext _) => const LoginWithPasswordPage(),
    loginNotPassword: (BuildContext _) => const LoginNotPasswordPage(),
    signUp: (BuildContext _) => const SignUpPage(),
    signUpVerificationCode: (BuildContext _) => const SignUpVerificationCodePage(),
    rewards: (BuildContext _) => const RewardsPage(),
    rewardDetail: (BuildContext _) => RewardsDetailWidget(),
    rewardAddnew: (BuildContext _) => const RewardsAddWidget(),
    rewardChangeIndex: (BuildContext _) => const RewardsChangeIndexWidget(),
    // settings: (BuildContext _) => const SettingsPage(),
    settings: (BuildContext _) => const SettingsPageNew(),
    labelsTemplate: (BuildContext _) => const LabelsTemplatePage(),
    labelTemplateCustom: (BuildContext _) => const LabelTemplateCustomPage(),
    codeScanned: (BuildContext _) => const CodeScanedPage(),
    staffs: (BuildContext _) => const StaffsPage(),
    staffAdd: (BuildContext _) => const StaffAddPage(),
    profileBusiness: (BuildContext _) => const ProfilePage(),
    activitiesPage: (BuildContext _) => const ActivitiesPage(),
    announcementPage: (BuildContext _) => const AnnouncementsPage(),
    announcementAddPage: (BuildContext _) => const AnnouncementAddPage(),
    announcementDetailPage: (BuildContext _) => const AnnouncementDetailPage(),
    announcementsChangeIndex: (BuildContext _) => const AnnouncementsChangeIndexWidget(),
    account: (BuildContext _) => const AccountPage(),
    referral: (BuildContext _) => const ReferralPage(),
    signageSettingTemp: (BuildContext _) => const SingageSettingTempPage(),
    signage: (BuildContext _) => const SignagePage(),
    signageCounter1: (BuildContext _) => const SignageCounterContent(),
    // signageRewardsTemplate1: (BuildContext _) => const SignageRewardsTemplate1Review(),
    signageRewardsTemplateReview: (BuildContext _) => const SignageRewardsTemplateReviewPage(),
    signageCounterPreview: (BuildContext _) => const SignageCounterCardPreview(),
    closeAccount: (BuildContext _) => const CloseAccountPage(),
    usagePage: (BuildContext _) => const StatisticsPage(),
    pendingApprovals: (BuildContext _) => const PendingApprovalsPage(),
    featureTourAnnoucement: (BuildContext _) => const FeatureTourPage(type: FeatureTourType.announcement),
    featureTourRewards: (BuildContext _) => const FeatureTourPage(type: FeatureTourType.rewards),
    featureTourReferral: (BuildContext _) => const FeatureTourPage(type: FeatureTourType.referral),
    customers: (BuildContext _) => const CustomersPage(),
    customerHistory: (BuildContext _) => const CustomerHistoryPage(),
    billingPayment: (BuildContext _) => const BillingPaymentMethodPage(),
    onboardingSetUpRewards: (BuildContext _) => const HomeOnboardingSetupRewards(),
    changePlan: (BuildContext _) => const ChangePlanPage(),
    firstScreen: (BuildContext _) => const FirstScreenPage(),
    inAppPlanPage: (BuildContext _) => const InAppPlanPage(),
    inAppPlanFreePage: (BuildContext _) => const InAppPlanFreePage(),
    offers: (BuildContext _) => const OffersPage(),
    labelTemplateOfferCustom: (BuildContext _) => const LabelTemplateOfferCustomPage(),
    loginWithPasswordByCustomer: (BuildContext _) => const LoginWithPasswordByCustomerPage(),
    addOffer: (BuildContext _) => const OfferCRUDMainPage(),
    detailOffer: (BuildContext _) => const OfferDetailPage(),
    customerPayment: (BuildContext _) => const CustomerPaymentPage(),
    signagePointClaims: (BuildContext _) => const SignagePointClaimsContent(),
    signagePointClaimsPreview: (BuildContext _) => const SignagePointClaimsPreview(),
    features: (BuildContext _) => const FeaturesPage(),
    featuresPointClaims: (BuildContext _) => const FeaturePointClaimsWidget(),
    callButtonPage: (BuildContext _) => const CallButtonPage(),
    cbService: (BuildContext _) => const CallButtonServicePage(),
    cbTable: (BuildContext _) => const CallButtonTablePage(),
    cbScanQRCode: (BuildContext _) => CBScanQRCodePage(),
    callButtonSettingSoundPage: (BuildContext _) => const CBSettingSoundPage(),
    callButtonSettingThanksPage: (BuildContext _) => const CBSettingThanksPage(),
    callButtonServiceAddNewService: (BuildContext _) => const CBServiceAddNewServicePage(),
    callButtonTableReorderPage: (BuildContext _) => const CallButtonTableReorderPage(),
    callButtonServiceReorderPage: (BuildContext _) => const CallButtonServiceReorderPage(),
    homeReorderWidgetSetting: (BuildContext _) => const HomeListSettingPage(),
    homeTemplate: (BuildContext _) => const HomeMultipleAddWidgetPage(),
    rolePermissionsPage: (BuildContext _) => const RolePermissionsPage(),
    permissionsPage: (BuildContext _) => const RolePermissionDetailPage(),
    callButtonHelpMeChoose: (BuildContext _) => const CallButtonHelpMeChoosePage(),
    callButtonHelpMeChooseAddData: (BuildContext _) => const CallButtonHelpMeChooseDataWidget(),
    settingGeneral: (BuildContext _) => const GeneralSettingWidget(),
    callButtonManage: (BuildContext _) => const CallButtonManage(),
    settingIntegrations: (BuildContext _) => const IntegrationsSettingWidget(),
    helpMeChoose: (BuildContext _) => const HelpMeChoosePage(),
    helpMeChooseAddData: (BuildContext _) => const HelpMeChooseDataWidget(),
    callButtonSettingCheckin: (BuildContext _) => const CallButtonSettingCheckInPage(),
    helpAndSupport: (BuildContext _) => const HelpAndSupportWidget(),
    orderActivePage: (BuildContext _) => const OrdersActivePage(),
    // orderActiveDetailPage: (BuildContext _) => const OrderActiveDetailPage(),
    orderActiveNotifPage: (BuildContext _) => const OrdersActiveNotificationPage(),
    managementProduct: (BuildContext _) => const CategoriesManagementPage(),
    managementProductDetail: (BuildContext _) => const ProductDetailPage(),
    orderHistory: (BuildContext _) => const OrderHistoryScreen(),
    settingsOrders: (BuildContext _) => const SettingsOrdersPage(),
    settingsOrdersAlert: (BuildContext _) => const SettingOrderAlertWidget(),
    settingsOrdersPrinter: (BuildContext _) => const SettingOrderReceiptPrinterPage(),
    settingsOrdersStoreHours: (BuildContext _) => const SettingOrderStoreHoursWidget(),
    updateStoreStatus: (BuildContext _) => const SettingOrderStoreStatusWidget(),
    integrationsPage: (BuildContext _) => const IntegrationsPage(),
    integrationsPointsPage: (BuildContext _) => const IntegrationsStickyPointsPage(),
    annDescriptionEnhanceAI: (BuildContext _) => const AnnDescriptionEnhanceAIPage(),
    chatBot: (BuildContext _) => const ChatBotWidget(),
    orderHistoryDetailPage: (BuildContext _) => const OrderHistoryDetailPage(),
    callButtonSettingReviewUsOnGooglePage: (BuildContext _) => const CallButtonSettingReviewUsOnGooglePage(),
    callButtonSignageCounterCardPreviewPage: (BuildContext _) => const CallButtonSignageCounterCardPreviewPage(),
    targetOffersPage: (BuildContext _) => const TargetOffersPage(),
    targetOfferDetailPage: (BuildContext _) => const TargetOfferDetailPage(),
    targetOffersRFMCustomerSegmentsPage: (BuildContext _) => const TargetOffersRFMCustomerSegmentsPage(),
    targetOfferCustomerPage: (BuildContext _) => const TargetOfferCustomerPage(),
    targetOfferDashboardPage: (BuildContext _) => const TargetOffersDashboardPage(),
    targetOfferDashboardPerformancePage: (BuildContext _) => const TODashboardOfferPerformancePage(),
    integrationsDeliveryPage: (BuildContext _) => const IntegrationsDeliveryPage(),
    hardwarePrinterpage: (BuildContext _) => const HardwarePrinterpage(),
    hardwarePrinterDetailPage: (BuildContext _) => const HardwarePrinterDetailPage(),
    hardwarePage: (BuildContext _) => const HardwarePage(),
    addNewDeliveryPage: (BuildContext _) => const AddNewDeliveryPage(),
    addNewDeliveryDateTimePage: (BuildContext _) => const AddNewDeliveryDateTimePage(),
    callButtonSettingTableStatusPage: (BuildContext _) => const CBSettingTableStatusPage(),
  };
}
