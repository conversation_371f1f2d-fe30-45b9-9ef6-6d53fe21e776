import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/models/section-table-detail.model.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/call-button-count-down-request/call-button-count-down-request.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class CallButtonRequestWidget extends StatelessWidget {
  final Request item;
  final bool isClicked;
  final int remainingSeconds;
  final VoidCallback onStartCountdown;
  final VoidCallback onComplete;
  final VoidCallback onUndo;

  const CallButtonRequestWidget({
    super.key,
    required this.item,
    required this.isClicked,
    required this.remainingSeconds,
    required this.onStartCountdown,
    required this.onComplete,
    required this.onUndo,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return InkWell(
      // onTap: () => onStartCountdown.call(),
      onTap: () {
        onStartCountdown.call();
        context.read<CallButtonCountdownRequestCubit>().addRequest(item);
      },
      child: Container(
        decoration: BoxDecoration(
          color: isClicked ? AppColors.appColor : Colors.white,
          border: const Border(
            bottom: BorderSide(
              width: 4,
              color: Color(0xFFE2E2E2),
            ),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildInfoRequest(context, l10n),
            _buildIcon(context, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRequest(BuildContext context, AppLocalizations l10n) {
    final timeZone = context.read<AuthBloc>().state.businessTimeZone;
    final time = DateTimeHelper.timeFormat(
      context,
      item.createdAt ?? DateTime.now(),
      timeZone: timeZone,
    );
    final requestName = _getRequestName();
    final requestNotes = item.notes ?? '';
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              requestName,
              style: TextStyle(
                color: _textColor(),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (requestNotes != '')
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                requestNotes,
                style: TextStyle(
                  color: _textColor().withValues(alpha: .6),
                  fontSize: 14,
                  // fontWeight: FontWeight.w600,
                ),
              ),
            ),
          if (isClicked)
            _buildCountdownText(l10n)
          else
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              margin: const EdgeInsets.only(top: 3),
              decoration: const BoxDecoration(
                color: Color(0xFFF5F5F5),
                borderRadius: BorderRadius.all(Radius.circular(9)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 15,
                    color: Color(0xFF999CA0),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    time,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black.withValues(alpha: .56),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildIcon(BuildContext context, AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.only(left: 12),
      child: Row(
        children: [
          if (isClicked)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: ButtonControlWidget(
                onPressed: () {
                  context.read<CallButtonCountdownRequestCubit>().addRequest(item);
                  onUndo.call();
                },
                height: 40,
                buttonText: l10n.homeUndo,
                fontSize: 16,
                buttonBackgroundColor: Colors.black.withValues(alpha: .1),
                borderRadius: 8,
              ),
            ),
          Transform.translate(
            offset: const Offset(5, 0),
            child: Checkbox(
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              value: isClicked,
              onChanged: (value) {
                if (value != null && value) {
                  onStartCountdown.call();
                  // context.read<CallButtonCountdownRequestCubit>().addRequest(item);
                } else {
                  onUndo.call();
                }
                context.read<CallButtonCountdownRequestCubit>().addRequest(item);
              },
              side: const BorderSide(color: Color(0xFF999999), width: 1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountdownText(AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 4, 4, 0),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: <InlineSpan>[
            TextSpan(
              text: l10n.homeCloseRequestAfter,
              style: TextStyle(
                fontSize: 16,
                color: _textColor(),
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: ' (${remainingSeconds}s)',
              style: TextStyle(
                fontSize: 16,
                color: _textColor(),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getRequestName() {
    if (item.isCustomRequest != null && item.isCustomRequest == true) {
      return item.customRequestName ?? '';
    }
    return item.service?.name ?? '';
  }

  Color _textColor() {
    if (isClicked) {
      return Colors.white;
    }
    return Colors.black;
  }
}
