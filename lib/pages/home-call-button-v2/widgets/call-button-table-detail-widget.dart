// ignore_for_file: use_decorated_box, library_private_types_in_public_api, inference_failure_on_function_invocation

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/models/section-table-detail.model.dart';
import 'package:stickyqrbusiness/@core/store/call-button-ably/call-button-ably.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-detail/call-button-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-sound/call-button-sound.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/rewards-active/rewards-active.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home-call-button-v2/widgets/@home-call-button-ver-2-widgets.dart';

class CallButtonTableDetail extends StatelessWidget {
  final CallButtonTable table;
  final ScrollController scrollController;
  const CallButtonTableDetail({
    super.key,
    required this.table,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => RewardsActiveCubit(),
        ),
      ],
      child: CallButtonTableDetailWidget(
        scrollController: scrollController,
        table: table,
      ),
    );
  }
}

class CallButtonTableDetailWidget extends StatefulWidget {
  final CallButtonTable table;
  final ScrollController scrollController;
  const CallButtonTableDetailWidget({
    super.key,
    required this.table,
    required this.scrollController,
  });

  @override
  State<CallButtonTableDetailWidget> createState() => _CallButtonTableDetailWidgetState();
}

class _CallButtonTableDetailWidgetState extends State<CallButtonTableDetailWidget> with WidgetsBindingObserver {
  late final l10n = context.l10n;

  // final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  List<Request> _services = [];
  final List<Request> _itemsToRemove = [];
  final Map<String, bool> _isClickedMap = {};
  final Map<String, int> _remainingSecondsMap = {};
  final Map<String, Timer?> _timersMap = {};
  CallButtonSoundCubit? _callButtonSoundCubit;

  @override
  void initState() {
    super.initState();
    _callButtonSoundCubit = context.read<CallButtonSoundCubit>();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    for (final timer in _timersMap.values) {
      timer?.cancel();
    }
    _callButtonSoundCubit?.onViewDetail(false);
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CallButtonAblyCubit, CallButtonAblyState>(
      listener: (context, state) {
        final type = state.type;
        final callButtonId = state.callButtonId;
        // AppLog.d('table id = ${state.tableId} \n callButtonId = ${state.callButtonId} \n serviceId = ${state.serviceId} \n userId = ${state.userId}\n note  = ${state.notes} ');
        switch (type) {
          // case TypeRealtime.callButtonSendRequest:
          //   context.read<CallButtonSoundCubit>().onChangeStatus(CallButtonSoundStatus.Stop);
          //   _onGetDetail(callButtonId ?? '');
          //   break;
          case TypeRealtime.callButtonSendRequest:
          case TypeRealtime.callButtonCancelRequest:
          case TypeRealtime.callButtonAllRequestDone:
          case TypeRealtime.callButtonRequestDone:
          case TypeRealtime.callButtonCustomerCheckin:
            _onGetDetail(callButtonId ?? '');
            break;
          case TypeRealtime.callButtonLeaveTable:
            context.read<CallButtonCubit>().onCloseOutTable(widget.table.id ?? '');
            if (context.mounted && Navigator.of(context).canPop()) {
              if (state.tableId == widget.table.id) {
                Navigator.of(context).pop();
              }
            }
            break;
          case TypeRealtime.callButtonCustomerCheckout:
            context.read<CallButtonDetailCubit>().onRemoveCheckin(state.checkinId ?? '');
            break;
          default:
        }
      },
      child: SafeArea(
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(),
          Expanded(
            child: CustomScrollView(
              controller: widget.scrollController,
              physics: const AlwaysScrollableScrollPhysics(
                parent: BouncingScrollPhysics(),
              ),
              slivers: [
                _buildSectionTitle(true),
                _buildListCheckin(),
                _buildListOrder(),
                _buildSectionTitle(false),
                _buildListServices(),
              ],
            ),
          ),
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      child: Column(
        children: [
          AppBar(
            automaticallyImplyLeading: false,
            backgroundColor: AppColors.lightPrimaryBackgroundColor,
            surfaceTintColor: Colors.transparent,
            centerTitle: true,
            title: Text(
              widget.table.name ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            actions: [
              InkWell(
                hoverColor: AppColors.lightPrimaryBackgroundColor,
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
                  child: SvgPicture.asset(
                    'assets/svgs/close.svg',
                    colorFilter: const ColorFilter.mode(
                      AppColors.appBlackColor,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Divider(
            thickness: 1,
            color: Color(0xFFEBEBEB),
            height: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return BlocBuilder<CallButtonDetailCubit, CallButtonDetailState>(
      builder: (context, state) {
        final table = state.tableDetail;
        final request = table?.requests ?? [];
        switch (state.status) {
          case CallButtonDetailStatus.Success:
          case CallButtonDetailStatus.Edit:
          case CallButtonDetailStatus.Progress:
            return Container(
              padding: const EdgeInsets.all(16.0),
              color: Colors.white,
              child: request.isNotEmpty
                  ? ButtonLoading(
                      callback: () {
                        context.read<CallButtonDetailCubit>().onSetAllRequestDone(widget.table.callButtonId ?? '').whenComplete(
                              () => context.read<CallButtonCubit>().onClearAllRequestsTable(widget.table.callButtonId ?? ''),
                            );
                      },
                      isLoading: state.status == CallButtonDetailStatus.Progress,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      label: l10n.homeMarkDoneCompleted,
                      height: 56,
                      buttonBackgroundColor: const Color(0xFF3448F0),
                    )
                  : ButtonLoading(
                      callback: () {
                        context.read<CallButtonDetailCubit>().onCloseOut(widget.table.callButtonId ?? '', table?.tableId ?? '');
                      },
                      isLoading: state.status == CallButtonDetailStatus.Progress,
                      label: l10n.homeCloseOut,
                      height: 56,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      buttonBackgroundColor: const Color(0xFF595D62),
                    ),
            );
          default:
            return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildListCheckin() {
    return BlocBuilder<CallButtonDetailCubit, CallButtonDetailState>(
      builder: (context, state) {
        final allCheckins = state.tableDetail?.checkins ?? [];
        final checkins = context.read<CallButtonCubit>().filterUserCheckin(allCheckins);
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            childCount: checkins.length,
            (BuildContext context, int index) {
              final checkin = checkins[index];
              final name = checkin.user != null && checkin.user?.displayName != null ? checkin.user?.displayName : checkin.userDisplayName ?? l10n.guest;
              final phone = checkin.user?.phone ?? checkin.userPhone ?? '';
              final uid = checkin.userId;
              return InkWell(
                onTap: () {
                  _onOpenCheckIn(widget.table, checkins, userCheckIn: checkin);
                },
                child: Container(
                  // margin: const EdgeInsets.symmetric(horizontal: 16),
                  // padding: const EdgeInsets.symmetric(vertical: 16),
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.only(top: 12, bottom: 16),
                  decoration: BoxDecoration(
                    border: index < checkins.length - 1
                        ? const Border(
                            bottom: BorderSide(
                            width: 1,
                            color: Color(0xFFEBEBEB),
                          ))
                        : null,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              name ?? '',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.black,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          // Text(
                          //   name ?? '',
                          //   style: const TextStyle(
                          //     fontSize: 16,
                          //     color: Colors.black,
                          //     fontWeight: FontWeight.w600,
                          //   ),
                          // ),
                          if (phone != '') ...[
                            // const Text(
                            //   ' - ',
                            //   style: TextStyle(
                            //     fontSize: 14,
                            //     fontWeight: FontWeight.normal,
                            //     color: Colors.black,
                            //   ),
                            // ),
                            Text(
                              AppValidations.formatPhoneNumber(phone),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Color(0xFF999CA0),
                              ),
                            ),
                          ],
                        ],
                      ),
                      // if (phone != '') ...[
                      //   Text(
                      //     AppValidations.formatPhoneNumber(phone),
                      //     style: const TextStyle(
                      //       fontSize: 14,
                      //       fontWeight: FontWeight.normal,
                      //       color: Color(0xFF999CA0),
                      //     ),
                      //   ),
                      // ],
                      BlocBuilder<CallButtonDetailCubit, CallButtonDetailState>(
                        builder: (context, state) {
                          final typeByID = state.selectedCustomer(uid ?? '')?.type;
                          return SegmentTargetOffersWidget(
                            title: typeByID ?? '',
                            uid: uid,
                            titleColor: AppColors.homeShowQRBGColor,
                            margin: const EdgeInsets.only(top: 2),
                          );
                        },
                      )
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(bool isCheckIn) {
    return BlocBuilder<CallButtonDetailCubit, CallButtonDetailState>(
      builder: (context, state) {
        final orders = _services.where((item) => item.service?.type == 'ORDER').toList();
        final allCheckins = state.tableDetail?.checkins ?? [];
        final checkins = context.read<CallButtonCubit>().filterUserCheckin(allCheckins);
        final serviceFilter = _services.where((item) => item.service?.type != 'ORDER').toList();
        if (isCheckIn) {
          return SliverToBoxAdapter(
            child: checkins.isNotEmpty
                ? Padding(
                    padding: const EdgeInsets.fromLTRB(15, 16, 4, 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${l10n.checkedIn.toUpperCase()} (${checkins.length})',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                        IconButton(
                          visualDensity: const VisualDensity(vertical: -4), // Giảm kích thước
                          padding: const EdgeInsets.all(4), // Padding tùy chỉnh
                          // constraints: const BoxConstraints(
                          //   minWidth: 24,
                          //   minHeight: 24,
                          // ),
                          onPressed: () {
                            _onOpenCheckIn(widget.table, checkins);
                          },
                          icon: SvgPicture.asset(
                            'assets/svgs/arrow-right-menu.svg',
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          );
        }
        return SliverToBoxAdapter(
          child: serviceFilter.isNotEmpty
              ? Column(
                  children: [
                    if (checkins.isNotEmpty && orders.isEmpty || orders.isNotEmpty)
                      const Padding(
                        padding: EdgeInsets.only(top: 8.0),
                        child: Divider(
                          thickness: 16,
                          color: Color(0xFFE2E2E2),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(15, 16, 16, 0),
                      child: Row(
                        children: [
                          Text(
                            '${l10n.request.toUpperCase()} (${serviceFilter.length})',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildListOrder() {
    return BlocBuilder<CallButtonDetailCubit, CallButtonDetailState>(
      builder: (context, state) {
        final allCheckins = state.tableDetail?.checkins ?? [];
        final checkins = context.read<CallButtonCubit>().filterUserCheckin(allCheckins);
        final orders = _services.where((item) => item.service?.type == 'ORDER').toList();
        final serviceFilter = _services.where((item) => item.service?.type != 'ORDER').toList();
        // AppLog.d('orders == ${jsonEncode(orders)}');
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            childCount: orders.length,
            (context, index) {
              final orderRequest = orders[index];
              return Container(
                color: Colors.white,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (orders.length > 1 && index != 0 && checkins.isEmpty || checkins.isNotEmpty)
                      const Padding(
                        padding: EdgeInsets.only(top: 6.0),
                        child: Divider(
                          thickness: 16,
                          color: Color(0xFFE2E2E2),
                        ),
                      ),
                    CallButtonOrderTableWidget(
                      request: orderRequest,
                      callback: () {},
                    ),
                    if (serviceFilter.isEmpty && index == orders.length - 1)
                      const Padding(
                        padding: EdgeInsets.only(top: 6.0),
                        child: Divider(
                          thickness: 16,
                          color: Color(0xFFE2E2E2),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildListServices() {
    return BlocConsumer<CallButtonDetailCubit, CallButtonDetailState>(
      listener: (context, state) {
        if (state.status == CallButtonDetailStatus.Success) {
          if (state.typeAction != CallButtonDetailTypeActions.SetPoints) {
            setState(() {
              final newRequests = state.tableDetail?.requests ?? [];
              _services = newRequests;
            });
          }
        } else if (state.status == CallButtonDetailStatus.Closed) {
          if (context.mounted && Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          }
        }
      },
      builder: (context, state) {
        final allCheckins = state.tableDetail?.checkins ?? [];
        final checkins = context.read<CallButtonCubit>().filterUserCheckin(allCheckins);
        // final checkins = context.read<CallButtonDetailCubit>().onFilterCheckins(listCheckIn);
        final serviceFilter = _services.where((item) => item.service?.type != 'ORDER').toList();
        final orders = _services.where((item) => item.service?.type == 'ORDER').toList();
        switch (state.status) {
          case CallButtonDetailStatus.Success:
          case CallButtonDetailStatus.Edit:
          case CallButtonDetailStatus.Progress:
            return _services.isNotEmpty
                ? SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (BuildContext context, int index) {
                        return _buildService(serviceFilter[index]);
                      },
                      childCount: serviceFilter.length,
                    ),
                  )
                : SliverToBoxAdapter(child: _buildEmpty(true, checkins, orders));
          case CallButtonDetailStatus.Loading:
            return SliverToBoxAdapter(child: _buildLoading());
          case CallButtonDetailStatus.Error:
            return SliverToBoxAdapter(
              child: _buildError(state.errorMsg ?? l10n.somethingWentWrong),
            );
          default:
            return SliverToBoxAdapter(child: _buildEmpty(true, checkins, orders));
        }
      },
    );
  }

  Widget _buildService(Request service) {
    final type = service.isCustomRequest != null && service.isCustomRequest == true ? 'REQUEST' : service.service?.type;
    switch (type) {
      // REQUEST - EXTERNAL_LINK - ACTION - QR_CODE
      case 'REQUEST':
        return _buildItem(service);
      case 'EXTERNAL_LINK':
        return const SizedBox.shrink();
      case 'ACTION':
        return _buildItemAction(service);
      case 'QR_CODE':
      case 'ORDER':
        return const SizedBox.shrink();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildItemAction(Request item) {
    final typeKey = item.service?.tyleKey;
    return KeepAliveWidget(
      key: ValueKey(item.id),
      child: typeKey == 'POINT_CLAIMS'
          ? CallButtonClaimPointsWidget(
              callback: () {
                _onUpdateSections(item.id ?? '');
                _itemsToRemove.add(item);
                _processRemovals();
              },
              request: item,
            )
          : CallButtonRedeemPointsWidget(
              callback: () {
                _onUpdateSections(item.id ?? '');
                _itemsToRemove.add(item);
                _processRemovals();
              },
              request: item,
            ),
    );
  }

  Widget _buildItem(Request item) {
    return KeepAliveWidget(
      key: ValueKey(item.id),
      child: CallButtonRequestWidget(
        item: item,
        isClicked: _isClickedMap[item.id] ?? false,
        remainingSeconds: _remainingSecondsMap[item.id] ?? 4,
        onStartCountdown: () {
          _startCountdown(item);
        },
        onComplete: () {
          setState(() {
            _itemsToRemove.add(item);
            _processRemovals();
          });
        },
        onUndo: () {
          setState(() {
            _timersMap[item.id]?.cancel();
            _timersMap.remove(item.id);
            _isClickedMap[item.id ?? ''] = false;
            _remainingSecondsMap.remove(item.id);
            _itemsToRemove.remove(item);
          });
        },
      ),
    );
  }

  Widget _buildEmpty(bool iRequest, List<Checkin> checkins, List<Request> orders) {
    return Container(
      alignment: Alignment.center,
      margin: iRequest && checkins.isNotEmpty || iRequest && orders.isNotEmpty ? const EdgeInsets.only(top: 16) : null,
      decoration: BoxDecoration(
          color: Colors.white,
          border: iRequest && checkins.isNotEmpty || orders.isNotEmpty
              ? const Border(
                  top: BorderSide(
                  width: 16,
                  color: Color(0xFFEBEBEB),
                ))
              : null),
      padding: const EdgeInsets.all(32),
      child: Text(
        l10n.homeThereAreNoRequests,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildError(String errMsg) {
    return Container(
      color: Colors.white,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 4),
                child: Icon(
                  Icons.warning_rounded,
                  color: Colors.red.shade400,
                ),
              ),
              Text(
                errMsg,
                style: const TextStyle(
                  // fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ButtonControlWidget(
            onPressed: () {
              context.read<CallButtonDetailCubit>().getTableDetail(widget.table.callButtonId ?? '');
            },
            buttonText: l10n.tryAgain,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ],
      ),
    );
  }

  Widget _buildLoading() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: ListView(
            controller: widget.scrollController,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              SkeletonLoadingWidget(
                itemCount: 5,
                paddingItem: EdgeInsets.zero,
                paddingList: EdgeInsets.zero,
                widget: SkeletonLoadingItem(
                  height: 200,
                  borderRadius: 0,
                ),
              ),
            ],
          ),
        ),
        ColoredBox(
          color: Colors.white,
          child: SkeletonLoadingWidget(
            paddingList: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            paddingItem: EdgeInsets.zero,
            itemCount: 1,
            widget: SkeletonLoadingItem(height: 56),
            isPadding: false,
          ),
        ),
      ],
    );
  }

  Future<void> _onOpenCheckIn(CallButtonTable table, List<Checkin>? checkins, {Checkin? userCheckIn}) async {
    final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;
    await showModalBottomSheet(
      useSafeArea: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: EdgeInsets.only(top: paddingTop),
        padding: MediaQuery.of(context).viewInsets,
        child: DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.7,
          maxChildSize: 1,
          minChildSize: 0.6,
          snap: true,
          snapSizes: const [0.7],
          builder: (context, scrollController) {
            return DecoratedBox(
              decoration: const BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: SafeArea(
                  top: false,
                  bottom: false,
                  child: CallButtonCustomersCheckInPage(
                    scrollController: scrollController,
                    table: table,
                    checkins: checkins ?? table.checkins ?? [],
                    userCheckIn: userCheckIn,
                    isFromDetail: true,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
    // .then((value) => context.read<CallButtonSoundCubit>().onViewDetail(false));
  }

  void _startCountdown(Request item) {
    final itemId = item.id ?? '';
    if (_isClickedMap[itemId] ?? false) return; // Check if countdown has already started
    setState(() {
      _isClickedMap[itemId] = true;
      _remainingSecondsMap[itemId] = 4;
      _timersMap[itemId] = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          if (_remainingSecondsMap[itemId]! > 0) {
            _remainingSecondsMap[itemId] = _remainingSecondsMap[itemId]! - 1;
          } else {
            if (_timersMap[itemId] != null) {
              _timersMap[itemId]?.cancel();
            }
            _removeItem(item);
            context.read<CallButtonDetailCubit>().onSetRequestDone(item.callButtonId ?? '', itemId).then((value) {
              _onUpdateSections(itemId);
            });
          }
        });
      });
    });
  }

  void _processRemovals() {
    while (_itemsToRemove.isNotEmpty) {
      _removeItem(_itemsToRemove.removeAt(0));
    }
  }

  void _removeItem(Request item) {
    final index = _services.indexOf(item);
    if (index != -1) {
      setState(() {
        _timersMap[item.id]?.cancel();
        _timersMap.remove(item.id);
        _isClickedMap.remove(item.id);
        _remainingSecondsMap.remove(item.id);

        _services.removeAt(index);
        _itemsToRemove.remove(item);
        // _listKey.currentState?.removeItem(
        //   index,
        //   (context, animation) => _buildService(item, index, animation), // Thay đổi ở đây
        //   duration: const Duration(milliseconds: 300),
        // );
      });
    }
  }

  void _onUpdateSections(String id) {
    context.read<CallButtonCubit>().onUpdateListTable(id);
  }

  void _onGetDetail(String callButtonId) {
    if (callButtonId == widget.table.callButtonId) {
      context.read<CallButtonDetailCubit>().getTableDetail(
            widget.table.callButtonId ?? '',
            withLoading: false,
          );
    }
  }

  void _buildCompleted() {
    context.read<CallButtonDetailCubit>().onResetStatus();
    context.read<CallButtonDetailCubit>().getTableDetail(widget.table.callButtonId ?? '');
  }
}

class KeepAliveWidget extends StatefulWidget {
  final Widget child;
  const KeepAliveWidget({Key? key, required this.child}) : super(key: key);

  @override
  _KeepAliveWidgetState createState() => _KeepAliveWidgetState();
}

class _KeepAliveWidgetState extends State<KeepAliveWidget> with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
