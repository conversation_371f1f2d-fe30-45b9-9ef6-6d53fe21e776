import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/store/call-button-ably/call-button-ably.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-customer-checkin/call-button-customer-checkin.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-detail/call-button-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/redeem/redeem.service.dart';
import 'package:stickyqrbusiness/@core/store/search-add-customer/search-add-customer.cubit.dart';
import 'package:stickyqrbusiness/@core/store/search-customer-by-id/search_customer_by_id.cubit.dart';
import 'package:stickyqrbusiness/@core/store/search-customer-by-phone/search_customer_by_phone.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/markdown-widget.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:url_launcher/url_launcher.dart';

class CallButtonCustomersCheckInPage extends StatelessWidget {
  final ScrollController scrollController;
  final CallButtonTable table;
  final List<Checkin>? checkins;
  final Checkin? userCheckIn;
  final ValueChanged<String>? onChanged;
  final bool? isFromDetail;
  const CallButtonCustomersCheckInPage({
    super.key,
    required this.scrollController,
    required this.table,
    required this.checkins,
    this.userCheckIn,
    this.onChanged,
    this.isFromDetail = false,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => CallButtonCustomerCheckInCubit()
            ..onResetStatus()
            ..onUpdateListCheckIn(checkins ?? table.checkins ?? [], userCheckIn: userCheckIn),
        ),
        BlocProvider(
          create: (context) => SearchCustomerByPhoneCubit(),
        ),
      ],
      child: CallButtonCustomersCheckInView(
        scrollController: scrollController,
        table: table,
        checkins: checkins ?? table.checkins ?? [],
        userCheckIn: userCheckIn,
        onChanged: onChanged,
        isFromDetail: isFromDetail,
      ),
    );
  }
}

class CallButtonCustomersCheckInView extends StatefulWidget {
  final ScrollController scrollController;
  final CallButtonTable table;
  final List<Checkin>? checkins;
  final Checkin? userCheckIn;
  final ValueChanged<String>? onChanged;
  final bool? isFromDetail;
  const CallButtonCustomersCheckInView({
    super.key,
    required this.scrollController,
    required this.table,
    required this.checkins,
    this.userCheckIn,
    this.onChanged,
    this.isFromDetail = false,
  });

  @override
  State<CallButtonCustomersCheckInView> createState() => _CallButtonCustomersCheckInViewState();
}

class _CallButtonCustomersCheckInViewState extends State<CallButtonCustomersCheckInView> with WidgetsBindingObserver {
  final Map<String, FocusNode> _focusNodes = {};
  final Map<String, bool> _isFocused = {};
  late CallButtonCustomerCheckInCubit _cubit;

  final Set<String> _fetchingIds = {};
  final Set<String> _fetchedIds = {};
  final Map<String, String?> _summaries = {};

  final Set<String> _fetchingBalanceIds = {};
  final Set<String> _fetchedBalanceIds = {};
  final Map<String, Search?> _balances = {};

  String? _processingCheckinId;
  final Map<String, num> _inputValues = {};

  @override
  void initState() {
    super.initState();
    _cubit = context.read<CallButtonCustomerCheckInCubit>();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    for (final node in _focusNodes.values) {
      node.dispose();
    }
    _fetchingIds.clear();
    _fetchedIds.clear();
    _summaries.clear();
    _fetchingBalanceIds.clear();
    _fetchedBalanceIds.clear();
    _balances.clear();
    _inputValues.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildContent(context, l10n));
  }

  Widget _buildContent(BuildContext context, AppLocalizations l10n) {
    return MultiBlocListener(
      listeners: [
        BlocListener<CallButtonDetailCubit, CallButtonDetailState>(
          listener: (context, state) {
            if (state.status == CallButtonDetailStatus.Success) {
              final checkins = state.tableDetail?.checkins ?? [];
              _cubit.onUpdateListCheckIn(checkins, userCheckIn: widget.userCheckIn);
              _initializeFocusNodes(checkins);
              _startFetchingSummaries(checkins);
              _startFetchingBalances(checkins);
            }
          },
        ),
        BlocListener<CallButtonCustomerCheckInCubit, CallButtonCustomerCheckInState>(
          listener: (context, state) {
            if (state.status == CallButtonCustomerCheckInStatus.SetPointsSuccess) {
              if (widget.isFromDetail == false) {
                widget.onChanged?.call('reload');
              }
              _onGetTableDetail();
            } else if (state.status == CallButtonCustomerCheckInStatus.Error) {
              if (state.errorMsg != null && state.errorMsg != '') {
                AppBased.toastError(context, title: state.errorMsg);
                _cubit.onRemoveErrMsg();
              }
            }
          },
        ),
        BlocListener<CallButtonAblyCubit, CallButtonAblyState>(
          listener: (context, state) {
            final type = state.type;
            switch (type) {
              case TypeRealtime.callButtonCustomerCheckin:
                if (widget.isFromDetail == false) {
                  _onGetTableDetail();
                }
                break;
              case TypeRealtime.callButtonCustomerCheckout:
                _cubit.onRemoveCheckIn(id: state.checkinId);
                break;
              default:
            }
          },
        ),
        BlocListener<SearchCustomerByPhoneCubit, SearchCustomerByPhoneState>(
          listener: (context, state) {
            switch (state.status) {
              case SearchCustomerByPhoneStatus.Error:
                if (state.errorMsg != null && state.errorMsg != '') {
                  AppBased.toastError(context, title: state.errorMsg);
                  BlocProvider.of<SearchCustomerByPhoneCubit>(context).onResetStatus();
                }
                _processingCheckinId = null;
                return;
              case SearchCustomerByPhoneStatus.CustomerNotFound:
                final data = state.data;
                _openDialogAddCustomer(context, data);
                return;
              case SearchCustomerByPhoneStatus.Success:
                if (_processingCheckinId != null) {
                  final data = {
                    'user': state.data,
                  };
                  AppBased.go(context, AppRoutes.redeemPoint, args: ScreenArguments(data: data));
                  _processingCheckinId = null;
                }
                return;
              default:
            }
          },
        ),
      ],
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeader(context, l10n),
            _buildListCustomer(l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, AppLocalizations l10n) {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      child: Column(
        children: [
          Container(
            width: 56,
            height: 8,
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: const Color(0xFFEBEBEB),
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          AppBar(
            automaticallyImplyLeading: false,
            backgroundColor: AppColors.lightPrimaryBackgroundColor,
            surfaceTintColor: Colors.transparent,
            centerTitle: true,
            title: Text(
              l10n.checkedIn,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            actions: [
              InkWell(
                hoverColor: AppColors.lightPrimaryBackgroundColor,
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
                  child: SvgPicture.asset(
                    'assets/svgs/close.svg',
                    colorFilter: const ColorFilter.mode(
                      AppColors.appBlackColor,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Divider(
            thickness: 1,
            color: Color(0xFFEBEBEB),
            height: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildListCustomer(AppLocalizations l10n) {
    return BlocBuilder<CallButtonCustomerCheckInCubit, CallButtonCustomerCheckInState>(
      builder: (context, state) {
        final allCheckins = state.listCheckIn ?? widget.checkins ?? [];
        final checkins = context.read<CallButtonCubit>().filterUserCheckin(allCheckins);
        return Expanded(
          child: NestedScrollView(
            physics: const NeverScrollableScrollPhysics(),
            headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
              return [];
            },
            body: checkins.isNotEmpty
                ? ListView.builder(
                    controller: widget.scrollController,
                    physics: const AlwaysScrollableScrollPhysics(
                      parent: BouncingScrollPhysics(),
                    ),
                    shrinkWrap: true,
                    itemCount: checkins.length,
                    itemBuilder: (context, index) {
                      return _buildCustomer(l10n, checkins[index]);
                    },
                  )
                : _buildEmpty(l10n),
          ),
        );
      },
    );
  }

  Widget _buildCustomer(AppLocalizations l10n, Checkin customerCheckIn) {
    final name = customerCheckIn.user != null && customerCheckIn.user?.displayName != null ? customerCheckIn.user?.displayName : customerCheckIn.userDisplayName ?? l10n.guest;
    final phone = customerCheckIn.user?.phone ?? customerCheckIn.userPhone ?? '';
    final checkinId = customerCheckIn.id;
    final enableEarnPoints = context.read<AuthBloc>().state.business?.callButtonCheckin?.allowEarnPoints ?? false;
    final uid = customerCheckIn.userId;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: 1,
            color: Color(0xFFD8D8D8),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: RichText(
                  textAlign: TextAlign.left,
                  softWrap: true,
                  text: TextSpan(
                    text: name,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    children: [
                      WidgetSpan(
                        child: Container(
                          margin: const EdgeInsets.only(left: 8),
                          width: 24,
                          height: 24,
                          child: BlocBuilder<SearchCustomerByPhoneCubit, SearchCustomerByPhoneState>(
                            builder: (context, state) {
                              final isLoading = state.status == SearchCustomerByPhoneStatus.Loading && _processingCheckinId == checkinId;
                              return isLoading
                                  ? Container(
                                      width: 16,
                                      height: 16,
                                      padding: const EdgeInsets.all(6),
                                      child: const CircularProgressIndicator(
                                        color: Color(0xFF3448F0),
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : IconButton(
                                      padding: EdgeInsets.zero,
                                      onPressed: () {
                                        if (checkinId != null) {
                                          _processingCheckinId = checkinId;
                                          context.read<SearchCustomerByPhoneCubit>().onSearchCustomerByPhone(userPhone: phone);
                                        }
                                      },
                                      icon: SvgPicture.asset(
                                        'assets/svgs/open-link.svg',
                                        colorFilter: const ColorFilter.mode(
                                          Color(0xFF3448F0),
                                          BlendMode.srcIn,
                                        ),
                                      ),
                                    );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              _buildBalanceInfo(l10n, customerCheckIn),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              BlocBuilder<CallButtonDetailCubit, CallButtonDetailState>(
                builder: (context, state) {
                  final typeByID = state.selectedCustomer(uid ?? '')?.type;
                  return SegmentTargetOffersWidget(
                    title: typeByID ?? '',
                    uid: uid,
                    titleColor: AppColors.homeShowQRBGColor,
                    margin: const EdgeInsets.only(top: 4),
                  );
                },
              ),
              if (phone != '') ...[
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    AppValidations.formatPhoneNumber(phone),
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.customersPhoneColor,
                    ),
                  ),
                )
              ] else
                const SizedBox.shrink(),
            ],
          ),
          if (enableEarnPoints && customerCheckIn.isDoneAdditionalPoints != null && customerCheckIn.isDoneAdditionalPoints == false) _buildInput(l10n, customerCheckIn),
          _buildSummaryInfo(l10n, customerCheckIn.id),
        ],
      ),
    );
  }

  Widget _buildSummaryInfo(AppLocalizations l10n, String? checkinId) {
    if (checkinId == null) {
      return const SizedBox.shrink();
    }
    // Đang fetch data
    if (_fetchingIds.contains(checkinId)) {
      return _buildLoadingState(l10n);
    }
    // Đã fetch xong và có data
    if (_fetchedIds.contains(checkinId)) {
      final summary = _summaries[checkinId];
      if (summary != null && summary.isNotEmpty) {
        return Padding(
          padding: const EdgeInsets.only(top: 16),
          child: MarkdownBodyWidget(
            content: summary,
          ),
          // child: MarkdownBody(
          //   selectable: true,
          //   data: summary,
          //   onTapLink: (text, href, title) {
          //     _launchURL(href ?? text);
          //   },
          // ),
        );
      }
      return const SizedBox.shrink();
    }
    // Chưa bắt đầu fetch
    return _buildLoadingState(l10n);
  }

  Widget _buildBalanceInfo(AppLocalizations l10n, Checkin checkin) {
    final String language = Localizations.localeOf(context).languageCode;
    final checkinId = checkin.id;
    if (checkinId == null) return const SizedBox.shrink();

    // Đang fetch balance
    if (_fetchingBalanceIds.contains(checkinId)) {
      return Container(
        padding: const EdgeInsets.only(top: 4),
        width: 50,
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: AppColors.appColor,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(3),
            child: const LinearProgressIndicator(
              value: null,
              minHeight: 6,
              backgroundColor: Color(0xFFE5E5E5),
              color: Colors.white,
            ),
          ),
        ),
      );
    }

    // Đã fetch xong và có data
    if (_fetchedBalanceIds.contains(checkinId)) {
      final balance = _balances[checkinId];
      if (balance != null) {
        final num total = num.parse(balance.totalPoints ?? '0');
        final totalPoints = AppValidations.getTotalPrice(total, language: language);
        return RichText(
          textAlign: TextAlign.right,
          softWrap: true,
          text: TextSpan(
            text: '${l10n.balance}: ',
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
            ),
            children: [
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Text(
                  total > 1 ? '$totalPoints ${language != 'vi' ? l10n.points : ''}' : '$totalPoints ${language != 'vi' ? l10n.point : ''}',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        );
      }
    }

    return const SizedBox.shrink();
  }

  Widget _buildInput(AppLocalizations l10n, Checkin customerCheckIn) {
    final additionalPoints = context.read<AuthBloc>().state.business?.callButtonCheckin?.additionalPoints.toString() ?? '';
    final id = customerCheckIn.id;
    if (id == null) return const SizedBox.shrink();
    if (!_focusNodes.containsKey(id)) {
      final focusNode = FocusNode();
      _focusNodes[id] = focusNode;
      _isFocused[id] = false;

      // Nếu item chưa có trong _inputValues và additionalPoints có giá trị
      if (!_inputValues.containsKey(id) && additionalPoints.isNotEmpty) {
        final parsedAdditionalPoints = num.tryParse(additionalPoints);
        if (parsedAdditionalPoints != null) {
          _inputValues[id] = parsedAdditionalPoints;
          // AppLog.d('Set initial value for item $id: ${_inputValues[id]}');
        }
      }
      focusNode.addListener(() {
        if (mounted) {
          setState(() {
            _isFocused[id] = focusNode.hasFocus;
            _cubit.onChangeInputPoints(num.parse('${_inputValues[id]}'));
          });
        }
      });
    }
    final initialValue = _inputValues.containsKey(id) ? _inputValues[id]?.toString() ?? '' : additionalPoints;
    // print('_inputValues.containsKey(id) = ${_inputValues.containsKey(id)}');

    return BlocBuilder<CallButtonCustomerCheckInCubit, CallButtonCustomerCheckInState>(
      builder: (context, state) {
        final isLoading = state.loadingButtons[id] ?? false;
        return Container(
          height: 48,
          margin: const EdgeInsets.only(top: 8),
          child: TextFormField(
            enableSuggestions: false,
            autocorrect: false,
            focusNode: _focusNodes[id],
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
            ],
            initialValue: initialValue,
            textAlignVertical: TextAlignVertical.center,
            decoration: InputDecoration(
              counterText: '',
              filled: true,
              fillColor: AppColors.lightPrimaryBackgroundColor,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(
                  color: Color(0xFF2C2C2C),
                  width: 1,
                ),
              ),
              isDense: true,
              hintText: l10n.enterPoints,
              hintStyle: const TextStyle(color: Color(0xFF909090)),
              suffixIcon: Padding(
                padding: const EdgeInsets.only(left: 8, right: 4),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: ButtonLoading(
                    borderRadius: 8,
                    iconDirection: TextDirection.rtl,
                    textDirection: TextDirection.rtl,
                    label: l10n.givePoints,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    buttonBackgroundColor: const Color(0xFF595D62),
                    labelColor: AppColors.lightPrimaryBackgroundColor,
                    circularStrokeColor: AppColors.appColor,
                    marginLoadingIcon: EdgeInsets.zero,
                    isAllowPress: additionalPoints != 0 && _inputValues[id] != 0 && !isLoading,
                    isLoading: isLoading,
                    callback: () async {
                      final points = _inputValues[id] ?? num.parse(additionalPoints);
                      await _cubit.onSetPointsCheckIn(widget.table.callButtonId ?? '', id, points);
                      if (mounted) {
                        unfocusAll();
                        _cubit.onChangeInputPoints(0);
                        await _refetchBalanceForSingleCheckin(customerCheckIn);
                      }
                    },
                  ),
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12),
            ),
            onChanged: (value) {
              if (value.isEmpty) {
                _inputValues[id] = 0;
                _cubit.onChangeInputPoints(0);
              } else {
                try {
                  final parsedValue = num.parse(value);
                  _inputValues[id] = parsedValue;
                  _cubit.onChangeInputPoints(parsedValue);
                  // AppLog.d('Changed value for item $id to: $parsedValue');
                } catch (e) {
                  print('Error parse number: $e');
                }
              }
              setState(() {});
            },
          ),
        );
      },
    );
  }

  Widget _buildEmpty(AppLocalizations l10n) {
    return Container(
      color: Colors.white,
      alignment: Alignment.center,
      padding: const EdgeInsets.all(32),
      child: Text(
        l10n.noUsersHaveCheckedIn,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildLoadingState(AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 4, top: 4),
            child: Text(
              l10n.searchingForInformationPleaseWait,
              style: const TextStyle(
                fontSize: 13,
                fontStyle: FontStyle.italic,
                color: Colors.grey,
              ),
            ),
          ),
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: AppColors.appColor,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(3),
              child: const LinearProgressIndicator(
                value: null,
                minHeight: 4,
                backgroundColor: Color(0xFFE5E5E5),
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _openDialogAddCustomer(
    BuildContext context,
    Search? data,
  ) async {
    final l10n = context.l10n;
    return showDialog(
      context: context,
      builder: (context) {
        return BlocProvider(
          create: (context) => AddCustomerCubit()..onChangedPhone(data?.phoneNumber ?? ''),
          child: BlocConsumer<AddCustomerCubit, AddCustomerState>(
            listener: (context, state) {
              switch (state.status) {
                case AddCustomerStatus.Error:
                  if (state.errorMsg != null && state.errorMsg != '') {
                    AppBased.toastError(context, title: state.errorMsg);
                    context.read<SearchCustomerByIDCubit>().onResetStatus();
                  }
                  return;
                case AddCustomerStatus.Success:
                  final data = {
                    'user': state.data,
                  };
                  Navigator.pop(context);
                  AppBased.go(context, AppRoutes.redeemPoint, args: ScreenArguments(data: data));
                  return;
                default:
              }
            },
            builder: (context, state) {
              return AlertDialog(
                scrollable: true,
                backgroundColor: AppColors.lightPrimaryBackgroundColor,
                insetPadding: const EdgeInsets.all(24),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.0),
                ),
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Center(
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          style: const TextStyle(
                            fontSize: 16,
                          ),
                          children: <InlineSpan>[
                            TextSpan(
                              text: l10n.phoneNumber,
                              style: const TextStyle(
                                color: AppColors.appBlackColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            WidgetSpan(
                              child: FutureBuilder<String>(
                                future: AppValidations.getPhoneNumber(data?.phoneNumber ?? ''),
                                builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
                                  final String? phone = (snapshot.data != null && snapshot.data != '') ? snapshot.data : data?.phoneNumber ?? '';

                                  return RichText(
                                    textAlign: TextAlign.center,
                                    text: TextSpan(
                                      text: ' $phone ',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.appColor,
                                      ),
                                      children: const [],
                                    ),
                                  );
                                },
                              ),
                            ),
                            TextSpan(
                              text: '${l10n.notFound.toLowerCase()}!',
                              style: const TextStyle(
                                color: AppColors.appBlackColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(top: 16),
                      alignment: Alignment.center,
                      child: Text(
                        l10n.addCustomer,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.appBlackColor.withValues(alpha: .56),
                        ),
                      ),
                    ),
                  ],
                ),
                actions: [
                  Row(
                    children: [
                      Expanded(
                        child: MaterialButton(
                          elevation: 0,
                          highlightElevation: 0,
                          hoverElevation: 0,
                          hoverColor: Colors.transparent,
                          padding: const EdgeInsets.all(16),
                          color: AppColors.lightPrimaryBackgroundColor,
                          textColor: AppColors.iconColor,
                          child: Text(
                            l10n.cancel.toUpperCase(),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                      ),
                      Expanded(
                        child: ButtonLoading(
                          borderRadius: 12,
                          height: 46,
                          label: l10n.add.toUpperCase(),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          padding: const EdgeInsets.only(left: 24, right: 24),
                          textDirection: TextDirection.rtl,
                          buttonBackgroundColor: AppColors.appColor,
                          // ignore: avoid_bool_literals_in_conditional_expressions
                          isLoading: state.status == AddCustomerStatus.Loading ? true : false,
                          callback: () => context.read<AddCustomerCubit>().doAddCustomer(),
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch');
    }
  }

  void unfocusAll() {
    for (final node in _focusNodes.values) {
      node.unfocus();
    }
  }

  Future<void> _startFetchingSummaries(List<Checkin> checkins) async {
    for (final checkin in checkins) {
      if (checkin.id != null && !_fetchingIds.contains(checkin.id)) {
        await _fetchSummaryForCheckin(checkin.id!);
      }
    }
  }

  Future<void> _fetchSummaryForCheckin(String checkinId) async {
    if (_fetchingIds.contains(checkinId) || _fetchedIds.contains(checkinId)) {
      return;
    }

    setState(() {
      _fetchingIds.add(checkinId);
    });

    try {
      final summary = await _cubit.getCustomerSummary(
        widget.table.id ?? '',
        checkinId,
        withLoading: false,
      );

      if (mounted) {
        setState(() {
          _fetchingIds.remove(checkinId);
          _fetchedIds.add(checkinId);
          _summaries[checkinId] = summary;
        });
      }
    } catch (e) {
      print('Error fetching summary for checkin $checkinId: $e');
      if (mounted) {
        setState(() {
          _fetchingIds.remove(checkinId);
        });
      }
    }
  }

  Future<void> _startFetchingBalances(List<Checkin> checkins) async {
    for (final checkin in checkins) {
      if (checkin.id != null && !_fetchingBalanceIds.contains(checkin.id)) {
        await _fetchBalanceForCheckin(checkin);
      }
    }
  }

  Future<void> _fetchBalanceForCheckin(Checkin checkin) async {
    final checkinId = checkin.id;
    final phone = checkin.user?.phone ?? checkin.userPhone;

    if (checkinId == null || phone == null || _fetchingBalanceIds.contains(checkinId) || _fetchedBalanceIds.contains(checkinId)) {
      return;
    }

    setState(() {
      _fetchingBalanceIds.add(checkinId);
    });

    try {
      final balance = await RedeemService().searchUserByPhone(checkin.user?.phone ?? checkin.userPhone ?? '');

      if (mounted) {
        setState(() {
          _fetchingBalanceIds.remove(checkinId);
          _fetchedBalanceIds.add(checkinId);
          _balances[checkinId] = balance;
        });
      }
    } catch (e) {
      print('Error fetching balance for checkin $checkinId: $e');
      if (mounted) {
        setState(() {
          _fetchingBalanceIds.remove(checkinId);
        });
      }
    }
  }

  Future<void> _refetchBalanceForSingleCheckin(Checkin checkin) async {
    final checkinId = checkin.id;
    final phone = checkin.user?.phone ?? checkin.userPhone;

    if (checkinId == null || phone == null) return;

    try {
      final balance = await RedeemService().searchUserByPhone(phone);
      if (mounted) {
        setState(() {
          _balances[checkinId] = balance;
          _fetchedBalanceIds.add(checkinId);
        });
      }
    } catch (e) {
      print('Error refetching balance for checkin $checkinId: $e');
    }
  }

  void _initializeFocusNodes(List<Checkin> checkins) {
    setState(() {
      for (final checkin in checkins) {
        final id = checkin.id;
        if (id != null && !_focusNodes.containsKey(id)) {
          final focusNode = FocusNode();
          _focusNodes[id] = focusNode;
          _isFocused[id] = false;

          focusNode.addListener(() {
            if (mounted) {
              setState(() {
                _isFocused[id] = focusNode.hasFocus;
              });
            }
          });
        }
      }
    });
  }

  void _onGetTableDetail() {
    context.read<CallButtonDetailCubit>().getTableDetail(
          widget.table.callButtonId ?? '',
          withLoading: false,
        );
  }

  void _buildCompleted() {
    final checkins = _cubit.state.listCheckIn ?? [];
    if (checkins.isNotEmpty) {
      _initializeFocusNodes(checkins);
      _startFetchingSummaries(checkins);
      _startFetchingBalances(checkins);
    }
  }
}
