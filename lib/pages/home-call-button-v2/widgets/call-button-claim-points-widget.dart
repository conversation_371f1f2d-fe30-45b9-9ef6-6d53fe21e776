// ignore_for_file: empty_catches, avoid_dynamic_calls

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/section-table-detail.model.dart';
import 'package:stickyqrbusiness/@core/store/call-button-detail/call-button-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-set-points/call-button-set-points.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class CallButtonClaimPointsWidget extends StatelessWidget {
  final VoidCallback callback;
  final Request request;
  const CallButtonClaimPointsWidget({
    super.key,
    required this.callback,
    required this.request,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => CallButtonSetPointsCubit(),
        ),
      ],
      child: CallButtonClaimPointsView(
        callback: callback,
        request: request,
      ),
    );
  }
}

class CallButtonClaimPointsView extends StatefulWidget {
  final VoidCallback callback;
  final Request request;
  const CallButtonClaimPointsView({
    super.key,
    required this.callback,
    required this.request,
  });

  @override
  State<CallButtonClaimPointsView> createState() => _CallButtonClaimPointsViewState();
}

class _CallButtonClaimPointsViewState extends State<CallButtonClaimPointsView> {
  final FocusNode _focusInput = FocusNode();
  late final l10n = context.l10n;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: MultiBlocListener(
        listeners: [
          BlocListener<CallButtonSetPointsCubit, CallButtonSetPointsState>(
            listener: (context, state) {
              if (state.status == CallButtonSetPointsStatus.Success) {
                context.read<CallButtonDetailCubit>().onSetRequestDone(
                      widget.request.callButtonId ?? '',
                      widget.request.id ?? '',
                    );
                widget.callback();
              } else if (state.status == CallButtonSetPointsStatus.Error) {
                // AppBased.toastError(context, title: state.errorMsg);
              }
            },
          ),
        ],
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            width: 4,
            color: Color(0xFFE2E2E2),
          ),
        ),
      ),
      child: Column(
        children: [
          _buildClaimPoints(),
          _buildError(),
        ],
      ),
    );
  }

  Widget _buildClaimPoints() {
    final language = Localizations.localeOf(context).languageCode;
    final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
    final userName = widget.request.user?.displayName ?? '';
    final timeCreated = DateTimeHelper.timeFormat(context, widget.request.createdAt, timeZone: timeZone);
    final uid = widget.request.userId;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            l10n.homeClaimPoints,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Row(
          children: [
            if (userName != '')
              Padding(
                padding: const EdgeInsets.only(right: 4),
                child: Text(
                  userName,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.homeShowQRBGColor,
                  ),
                ),
              ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                color: Color(0xFFF5F5F5),
                borderRadius: BorderRadius.all(Radius.circular(9)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 15,
                    color: Color(0xFF999CA0),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    timeCreated,
                    style: TextStyle(
                      color: Colors.black.withValues(alpha: .56),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        BlocBuilder<CallButtonDetailCubit, CallButtonDetailState>(
          builder: (context, state) {
            final typeByID = state.selectedCustomer(uid ?? '')?.type;
            return SegmentTargetOffersWidget(
              title: typeByID ?? '',
              uid: uid,
              titleColor: AppColors.homeShowQRBGColor,
              margin: const EdgeInsets.only(top: 2),
            );
          },
        ),
        Padding(
          padding: const EdgeInsets.only(top: 12),
          child: BlocBuilder<CallButtonSetPointsCubit, CallButtonSetPointsState>(
            builder: (context, state) {
              final String points = state.points ?? '';
              return Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Container(
                      height: 40,
                      // constraints: const BoxConstraints(maxWidth: 250),
                      margin: const EdgeInsets.only(right: 16),
                      child: TextFormField(
                        cursorColor: AppColors.cursorColor,
                        focusNode: _focusInput,
                        // enableSuggestions: false,
                        autocorrect: false,
                        textInputAction: TextInputAction.done,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                        ],
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppColors.darkPrimaryBackgroundColor,
                          fontWeight: FontWeight.w600,
                        ),
                        scrollPadding: const EdgeInsets.only(bottom: 100),
                        textAlignVertical: TextAlignVertical.center,
                        decoration: InputDecoration(
                          hintStyle: const TextStyle(
                            color: AppColors.accountLabelColor,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                          contentPadding: const EdgeInsets.only(left: 16),
                          isCollapsed: true,
                          counterText: '',
                          filled: true,
                          fillColor: AppColors.lightPrimaryBackgroundColor,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              width: 1.5,
                              color: AppColors.darkPrimaryBackgroundColor,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              width: 1,
                              color: AppColors.appBorderColor,
                            ),
                          ),
                          hintText: l10n.homeEnterPoints,
                          suffixIcon: Container(
                            margin: const EdgeInsets.all(3),
                            child: ButtonLoading(
                              width: 80,
                              borderRadius: 6,
                              padding: EdgeInsets.zero,
                              isAllowPress: points != '' && points != '0',
                              isLoading: state.status == CallButtonSetPointsStatus.Loading,
                              iconDirection: TextDirection.rtl,
                              textDirection: TextDirection.rtl,
                              label: l10n.homeGive,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              labelColor: AppColors.lightPrimaryBackgroundColor,
                              circularStrokeColor: AppColors.appColor,
                              marginLoadingIcon: EdgeInsets.zero,
                              labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                              buttonBackgroundColor: points != '' ? AppColors.appColor : const Color(0xFFCCCCCC),
                              callback: () {
                                FocusScope.of(context).unfocus();
                                AppBased.showDialogYesNo(
                                  context,
                                  msgContent: l10n.pointClaimGiveConfirm(points, int.parse(points.replaceAll(',', '').replaceAll('.', '')) > 1 ? 's' : ''),
                                  title: l10n.confirm,
                                  yesText: l10n.confirm,
                                  noText: l10n.cancel,
                                  yesTap: () {
                                    context.read<CallButtonSetPointsCubit>().onSetPoints(requestId: widget.request.id);
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                        controller: TextEditingController.fromValue(
                          TextEditingValue(
                            text: state.points == '0' ? '' : state.points ?? '',
                            selection: TextSelection.collapsed(
                              offset: points == '0' ? 0 : state.points?.length ?? 0,
                            ),
                          ),
                        ),
                        onChanged: (value) {
                          try {
                            if (value.length > 3) {
                              value = AppValidations.getTotalPrice(num.parse(value), language: language);
                            }
                            // context.read<PointClaimActionCubit>().onChangePoint(value);
                            context.read<CallButtonSetPointsCubit>().onChangedPoints(value);
                          } catch (e) {}
                        },
                      ),
                    ),
                  ),
                  ButtonControlWidget(
                    onPressed: () {
                      context.read<CallButtonDetailCubit>().onSetRequestDone(
                            widget.request.callButtonId ?? '',
                            widget.request.id ?? '',
                            // typeActions: CallButtonDetailTypeActions.SetPoints,
                          );
                      widget.callback();
                    },
                    height: 40,
                    buttonText: l10n.homeReject,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    buttonTextColor: const Color(0xFFFF4E64),
                    buttonBackgroundColor: const Color(0xFFFFF4F6),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildError() {
    return BlocBuilder<CallButtonSetPointsCubit, CallButtonSetPointsState>(
      builder: (context, state) {
        final isError = state.errorMsg != null && state.errorMsg != '';
        final errMsg = state.errorCode == '400' ? l10n.somethingWentWrong : state.errorMsg;
        if (isError) {
          return Container(
            padding: const EdgeInsets.all(8),
            margin: const EdgeInsets.only(top: 16),
            decoration: const BoxDecoration(
              color: Color(0xFFFDEDF0),
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const Icon(Icons.info, color: Color(0xFFDF1C41), size: 16),
                const SizedBox(width: 6),
                Flexible(
                  child: Text(
                    errMsg ?? '',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF0A0D14),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          return const SizedBox(height: 8);
        }
      },
    );
  }
}
