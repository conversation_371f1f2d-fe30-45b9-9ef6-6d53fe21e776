import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/models/section-table-detail.model.dart';
import 'package:stickyqrbusiness/@core/store/call-button-count-down-request-orders/call-button-count-down-request-orders.cubit.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class CallButtonProductWidget extends StatelessWidget {
  final Request request;
  final CallButtonRequestProduct product;
  final bool isClicked;
  final int remainingSeconds;
  final VoidCallback onStartCountdown;
  final VoidCallback onComplete;
  final VoidCallback onUndo;

  const CallButtonProductWidget({
    super.key,
    required this.request,
    required this.product,
    required this.isClicked,
    required this.remainingSeconds,
    required this.onStartCountdown,
    required this.onComplete,
    required this.onUndo,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return InkWell(
      onTap: () {
        onStartCountdown.call();
        context.read<CallButtonCountdownRequestOrdersCubit>().addRequest(request, product);
      },
      child: Container(
        decoration: BoxDecoration(
          color: isClicked ? AppColors.appColor : Colors.white,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildItemProduct(context, l10n),
            _buildIcon(context, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildItemProduct(BuildContext context, AppLocalizations l10n) {
    final requestName = product.name ?? '';
    final quantity = product.quantity ?? 1;
    final modifiers = product.modifiers ?? [];
    final requestNotes = request.notes ?? '';
    final productNotes = product.notes ?? '';
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                '$quantity  x',
                style: TextStyle(
                  color: _textColor(),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        requestName,
                        style: TextStyle(
                          color: _textColor(),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (modifiers.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        ...modifiers.asMap().entries.map(
                          (entry) {
                            final item = entry.value;
                            final options = item.options ?? [];
                            return Padding(
                              padding: const EdgeInsets.only(top: 6),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.optionName ?? '',
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: _textModifierColor(),
                                    ),
                                  ),
                                  if (options.isNotEmpty) ...[
                                    ...options.map(
                                      (option) {
                                        final optionName = option.name ?? '';
                                        if (optionName == '') return const SizedBox.shrink();
                                        return Padding(
                                          padding: const EdgeInsets.only(left: 16.0),
                                          child: Text(
                                            '• ${option.name ?? ''}',
                                            textAlign: TextAlign.left,
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: _textModifierColor(),
                                            ),
                                          ),
                                        );
                                      },
                                    ).toList(),
                                  ],
                                ],
                              ),
                            );
                          },
                        ).toList(),
                      ],
                      if (productNotes != '')
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            '${l10n.note}: $productNotes',
                            style: TextStyle(
                              color: _textModifierColor(),
                              fontSize: 16,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          if (requestNotes != '')
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Text(
                '${l10n.note}: $requestNotes',
                style: TextStyle(
                  color: _textModifierColor(),
                  fontSize: 16,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          if (isClicked) _buildCountdownText(l10n)
        ],
      ),
    );
  }

  Widget _buildIcon(BuildContext context, AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.only(left: 12),
      child: Row(
        children: [
          if (isClicked)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: ButtonControlWidget(
                onPressed: () {
                  context.read<CallButtonCountdownRequestOrdersCubit>().addRequest(request, product);
                  onUndo.call();
                },
                height: 40,
                buttonText: l10n.homeUndo,
                fontSize: 16,
                buttonBackgroundColor: Colors.black.withValues(alpha: .1),
                borderRadius: 8,
              ),
            ),
          Transform.translate(
            offset: const Offset(5, 0),
            child: Checkbox(
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              value: isClicked,
              onChanged: (value) {
                if (value != null && value) {
                  onStartCountdown.call();
                } else {
                  onUndo.call();
                }
                context.read<CallButtonCountdownRequestOrdersCubit>().addRequest(request, product);
              },
              side: const BorderSide(color: Color(0xFF999999), width: 1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountdownText(AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 4, 4, 0),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: <InlineSpan>[
            TextSpan(
              text: l10n.homeCloseRequestAfter,
              style: TextStyle(
                fontSize: 16,
                color: _textColor(),
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: ' (${remainingSeconds}s)',
              style: TextStyle(
                fontSize: 16,
                color: _textColor(),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _textColor() {
    if (isClicked) {
      return Colors.white;
    }
    return Colors.black;
  }

  Color _textModifierColor() {
    if (isClicked) {
      return Colors.white;
    }
    return Colors.black54;
  }
}
