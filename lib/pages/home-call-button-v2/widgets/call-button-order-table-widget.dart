import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/models/section-table-detail.model.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/call-button-count-down-request-orders/call-button-count-down-request-orders.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-detail/call-button-detail.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/segment-target-offers-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home-call-button-v2/widgets/@home-call-button-ver-2-widgets.dart';

class CallButtonOrderTableWidget extends StatelessWidget {
  final VoidCallback callback;
  final Request request;
  const CallButtonOrderTableWidget({
    super.key,
    required this.callback,
    required this.request,
  });

  @override
  Widget build(BuildContext context) {
    AppLog.e('request...: ${request.toJson()}');
    return CallButtonOrderTableWidgetContent(
      callback: callback,
      request: request,
    );
  }
}

class CallButtonOrderTableWidgetContent extends StatefulWidget {
  final VoidCallback callback;
  final Request request;
  const CallButtonOrderTableWidgetContent({
    super.key,
    required this.callback,
    required this.request,
  });

  @override
  State<CallButtonOrderTableWidgetContent> createState() => _CallButtonOrderTableWidgetContentState();
}

class _CallButtonOrderTableWidgetContentState extends State<CallButtonOrderTableWidgetContent> with WidgetsBindingObserver, TickerProviderStateMixin {
  late final ExpansionTileController _expansionController = ExpansionTileController();
  late final l10n = context.l10n;

  List<CallButtonRequestProduct> _products = [];
  final List<CallButtonRequestProduct> _itemsToRemove = [];
  final Map<String, bool> _isClickedMap = {};
  final Map<String, int> _remainingSecondsMap = {};
  final Map<String, Timer?> _timersMap = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didUpdateWidget(CallButtonOrderTableWidgetContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.request != widget.request) {
      setState(() {
        _products = widget.request.products ?? [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_products.isEmpty) ...{
            const SizedBox.shrink(),
          } else ...{
            Flexible(
              child: Stack(
                children: [
                  _buildBlockCallbutton(),
                  _buildHeader(),
                ],
              ),
            ),
          },
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
    final userName = widget.request.user?.displayName ?? '';
    final timeCreated = DateTimeHelper.timeFormat(context, widget.request.createdAt, timeZone: timeZone);
    final serviceName = widget.request.service?.name?.toUpperCase() ?? '';
    final productsCount = widget.request.products?.length ?? 0;
    final uid = widget.request.userId;
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.only(right: 16.0, bottom: 8),
                  child: Text(
                    serviceName,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ),
              BlocBuilder<CallButtonCountdownRequestOrdersCubit, CallButtonCountDownRequestOrdersState>(
                builder: (context, state) {
                  final isExpand = state.isExpanded;
                  return GestureDetector(
                    onTap: () => _toggleExpansion(),
                    child: Container(
                      height: 28,
                      width: 48,
                      padding: const EdgeInsets.all(2),
                      margin: const EdgeInsets.only(bottom: 6),
                      decoration: BoxDecoration(
                        color: AppColors.appBlueColor.withValues(alpha: .12),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: SvgPicture.asset(
                        isExpand ? 'assets/svgs/arrow-up.svg' : 'assets/svgs/arrow-down.svg',
                        colorFilter: const ColorFilter.mode(
                          AppColors.darkPrimaryBackgroundColor,
                          BlendMode.srcIn,
                        ),
                        width: 24,
                        height: 24,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              '$productsCount ${productsCount > 1 ? l10n.callButtonOrderItems.toLowerCase() : l10n.callButtonOrderItem.toLowerCase()}',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (userName != '')
                Flexible(
                  child: Text(
                    userName,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.homeShowQRBGColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              Container(
                margin: const EdgeInsets.only(left: 12),
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                alignment: Alignment.center,
                decoration: const BoxDecoration(
                  color: Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.all(Radius.circular(9)),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.access_time,
                      size: 15,
                      color: Color(0xFF999CA0),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      timeCreated,
                      style: TextStyle(
                        color: Colors.black.withValues(alpha: .56),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          BlocBuilder<CallButtonDetailCubit, CallButtonDetailState>(
            builder: (context, state) {
              final typeByID = state.selectedCustomer(uid ?? '')?.type;
              return Container(
                padding: const EdgeInsets.only(bottom: 2.0),
                child: SegmentTargetOffersWidget(
                  title: typeByID ?? '',
                  uid: uid,
                  titleColor: AppColors.homeShowQRBGColor,
                ),
              );
            },
          )
        ],
      ),
    );
  }

  Widget _buildBlockCallbutton() {
    return BlocBuilder<CallButtonCountdownRequestOrdersCubit, CallButtonCountDownRequestOrdersState>(
      builder: (context, state) {
        final isExpand = state.isExpanded;

        return BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            final enableTargetOffers = state.business?.enableTargetOffers ?? false;
            return ExpansionTile(
              minTileHeight: enableTargetOffers ? 72 : 72,
              controller: _expansionController,
              initiallyExpanded: isExpand,
              backgroundColor: Colors.transparent,
              shape: const Border(),
              title: GestureDetector(
                onTap: () {},
                child: const SizedBox.shrink(),
              ),
              trailing: const SizedBox.shrink(),
              children: <Widget>[
                _buildListProducts(),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildListProducts() {
    final products = widget.request.products ?? [];
    return BlocConsumer<CallButtonDetailCubit, CallButtonDetailState>(
      listener: (context, state) {
        if (state.status == CallButtonDetailStatus.Success) {
          if (state.typeAction != CallButtonDetailTypeActions.SetPoints) {
            setState(() {
              final newRequests = state.tableDetail?.requests ?? [];
              final matchingRequest = newRequests.firstWhere(
                (item) => item.id == widget.request.id,
                orElse: () => widget.request,
              );
              _products = matchingRequest.products ?? [];
            });
          }
        } else if (state.status == CallButtonDetailStatus.Closed) {
          if (context.mounted && Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          }
        }
        print('_products == ${_products.length}');
      },
      builder: (context, state) {
        return ListView.builder(
          padding: const EdgeInsets.only(top: 48),
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return Column(
              children: [
                CallButtonProductWidget(
                  request: widget.request,
                  product: product,
                  isClicked: _isClickedMap[product.id] ?? false,
                  remainingSeconds: _remainingSecondsMap[product.id] ?? 4,
                  onStartCountdown: () {
                    _startCountdown(product);
                  },
                  onComplete: () {
                    setState(() {
                      _itemsToRemove.add(product);
                      _processRemovals();
                    });
                  },
                  onUndo: () {
                    setState(() {
                      _timersMap[product.id]?.cancel();
                      _timersMap.remove(product.id);
                      _isClickedMap[product.id ?? ''] = false;
                      _remainingSecondsMap.remove(product.id);
                      _itemsToRemove.remove(product);
                    });
                  },
                ),
                if (index < products.length - 1)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Divider(
                      height: 8,
                      color: Colors.grey.shade300,
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _toggleExpansion() {
    if (_expansionController.isExpanded) {
      _expansionController.collapse();
    } else {
      _expansionController.expand();
    }
    context.read<CallButtonCountdownRequestOrdersCubit>().onChangeExpand(_expansionController.isExpanded);
  }

  void _startCountdown(CallButtonRequestProduct item) {
    final callButtonId = widget.request.callButtonId ?? '';
    final requestId = widget.request.id ?? '';
    final itemId = item.id ?? '';
    if (_isClickedMap[itemId] ?? false) return;
    setState(() {
      _isClickedMap[itemId] = true;
      _remainingSecondsMap[itemId] = 4;
      _timersMap[itemId] = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          if (_remainingSecondsMap[itemId]! > 0) {
            _remainingSecondsMap[itemId] = _remainingSecondsMap[itemId]! - 1;
          } else {
            if (_timersMap[itemId] != null) {
              _timersMap[itemId]?.cancel();
            }
            _removeItem(item);
            context.read<CallButtonDetailCubit>().onSetRequestDone(callButtonId, requestId, itemId: itemId).then((value) {
              // _onUpdateSections(itemId);
            });
          }
        });
      });
    });
  }

  void _processRemovals() {
    while (_itemsToRemove.isNotEmpty) {
      _removeItem(_itemsToRemove.removeAt(0));
    }
  }

  void _removeItem(CallButtonRequestProduct item) {
    final index = _products.indexOf(item);
    if (index != -1) {
      setState(() {
        _timersMap[item.id]?.cancel();
        _timersMap.remove(item.id);
        _isClickedMap.remove(item.id);
        _remainingSecondsMap.remove(item.id);

        _products.removeAt(index);
        _itemsToRemove.remove(item);
      });
    }
  }

  void _buildCompleted() {
    setState(() {
      _products = widget.request.products ?? [];
    });
  }
}
