// ignore_for_file: use_decorated_box, library_private_types_in_public_api, inference_failure_on_function_invocation

import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/app-based.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/models/section-table-detail.model.dart';
import 'package:stickyqrbusiness/@core/store/call-button-ably/call-button-ably.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-close-out-inactive/call-button-close-out-inactive.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-detail/call-button-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-sound/call-button-sound.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/rewards-active/rewards-active.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home-call-button-v2/widgets/@home-call-button-ver-2-widgets.dart';

class CallButtonCloseOutTableWidget extends StatelessWidget {
  final List<CallButtonTable> tables;
  final ScrollController scrollController;
  const CallButtonCloseOutTableWidget({
    super.key,
    required this.tables,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        // BlocProvider(
        //   create: (context) => RewardsActiveCubit(),
        // ),
        BlocProvider(
          create: (context) => CallButtonCloseOutInactiveCubit(),
        ),
      ],
      child: CallButtonCloseOutTableWidgetWidget(
        scrollController: scrollController,
        tables: tables,
      ),
    );
  }
}

class CallButtonCloseOutTableWidgetWidget extends StatefulWidget {
  final List<CallButtonTable> tables;
  final ScrollController scrollController;
  const CallButtonCloseOutTableWidgetWidget({
    super.key,
    required this.tables,
    required this.scrollController,
  });

  @override
  State<CallButtonCloseOutTableWidgetWidget> createState() => _CallButtonCloseOutTableWidgetWidgetState();
}

class _CallButtonCloseOutTableWidgetWidgetState extends State<CallButtonCloseOutTableWidgetWidget> with WidgetsBindingObserver {
  late final l10n = context.l10n;

  // final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  List<Request> _services = [];
  final List<Request> _itemsToRemove = [];
  final Map<String, bool> _isClickedMap = {};
  final Map<String, int> _remainingSecondsMap = {};
  final Map<String, Timer?> _timersMap = {};
  CallButtonSoundCubit? _callButtonSoundCubit;

  @override
  void initState() {
    super.initState();
    _callButtonSoundCubit = context.read<CallButtonSoundCubit>();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    for (final timer in _timersMap.values) {
      timer?.cancel();
    }
    _callButtonSoundCubit?.onViewDetail(false);
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // BlocListener<CallButtonAblyCubit, CallButtonAblyState>(
        //   listener: (context, state) {
        //     final type = state.type;
        //     final callButtonId = state.callButtonId;
        //     // AppLog.d('table id = //${state.tableId} \n callButtonId = ${state.callButtonId} \n serviceId = ${state.serviceId} \n userId = ${state.userId}\n note  = ${state.notes} ');
        //     switch (type) {
        //       // case TypeRealtime.callButtonSendRequest:
        //       //   context.read<CallButtonSoundCubit>().onChangeStatus(CallButtonSoundStatus.Stop);
        //       //   _onGetDetail(callButtonId ?? '');
        //       //   break;
        //       case TypeRealtime.callButtonSendRequest:
        //       case TypeRealtime.callButtonCancelRequest:
        //       case TypeRealtime.callButtonAllRequestDone:
        //       case TypeRealtime.callButtonRequestDone:
        //       case TypeRealtime.callButtonCustomerCheckin:
        //         _onGetDetail(callButtonId ?? '');
        //         break;
        //       case TypeRealtime.callButtonLeaveTable:
        //         // context.read<CallButtonCubit>().onCloseOutTable(widget.table.id ?? '');
        //         // if (context.mounted && Navigator.of(context).canPop()) {
        //         //   if (state.tableId == widget.table.id) {
        //         //     Navigator.of(context).pop();
        //         //   }
        //         // }
        //         break;
        //       case TypeRealtime.callButtonCustomerCheckout:
        //         context.read<CallButtonDetailCubit>().onRemoveCheckin(state.checkinId ?? '');
        //         break;
        //       default:
        //     }
        //   },
        // ),
        BlocListener<CallButtonCloseOutInactiveCubit, CallButtonCloseOutInactiveState>(
          listener: (context, state) {
            if (state.status == CallButtonCloseOutInactiveStatus.Success) {
              final allTables = state.tablesInactive ?? [];
              AppBased.toastSuccess(context, title: l10n.successfully, duration: 1);
              if (allTables.isNotEmpty) {
                // final tablesInactive = _getInactiveTables(allSections);
                // context.read<CallButtonCloseOutInactiveCubit>().onUpdateTablesInactive(tablesInactive);
                Navigator.of(context).pop();
              }
              AppLog.e('close out page listen: ${jsonEncode(state.tablesInactive)}');
            } else if (state.status == CallButtonCloseOutInactiveStatus.Error) {
              if (state.errorMsg != null && state.errorMsg != '') {
                AppBased.toastError(context, title: state.errorMsg);
                context.read<CallButtonCloseOutInactiveCubit>().onRemoveMsg();
              }
            }
          },
        ),
        BlocListener<CallButtonCubit, CallButtonState>(
          listener: (context, state) {
            if (state.status == CallButtonStatus.Success) {
              final allSections = state.allSections ?? [];
              if (allSections.isNotEmpty) {
                final tablesInactive = _getInactiveTables(allSections);
                context.read<CallButtonCloseOutInactiveCubit>().onUpdateTablesInactive(tablesInactive);
              }
            }
          },
        ),
      ],
      child: SafeArea(child: _buildBody()),
    );
  }

  Widget _buildBody() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(),
          Expanded(
            child: CustomScrollView(
              controller: widget.scrollController,
              physics: const AlwaysScrollableScrollPhysics(
                parent: BouncingScrollPhysics(),
              ),
              slivers: [
                // _buildSectionTitle(true),
                _buildSelectAllCheckbox(),
                _buildListTables(),
                // _buildListCheckin(),
                // _buildListOrder(),
                // _buildSectionTitle(false),
                // _buildListServices(),
              ],
            ),
          ),
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      child: Column(
        children: [
          AppBar(
            automaticallyImplyLeading: false,
            backgroundColor: AppColors.lightPrimaryBackgroundColor,
            surfaceTintColor: Colors.transparent,
            centerTitle: true,
            title: Text(
              l10n.homeCallBtnCloseOutInactiveTable,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            actions: [
              InkWell(
                hoverColor: AppColors.lightPrimaryBackgroundColor,
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
                  child: SvgPicture.asset(
                    'assets/svgs/close.svg',
                    colorFilter: const ColorFilter.mode(
                      AppColors.appBlackColor,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Divider(
            thickness: 1,
            color: Color(0xFFEBEBEB),
            height: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return BlocBuilder<CallButtonCloseOutInactiveCubit, CallButtonCloseOutInactiveState>(
      builder: (context, state) {
        // final tables = state.tablesInactive ?? [];
        final ids = state.slectedIds ?? [];
        final isSelectedAll = state.isSlectedAll ?? false;
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          child: Row(
            children: [
              Expanded(
                child: ButtonLoading(
                  callback: () {
                    Navigator.of(context).pop();
                  },
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  label: l10n.cancel,
                  height: 56,
                  labelColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: const BorderSide(
                      width: 1,
                      color: Color(0xFF999CA0),
                    ),
                  ),
                  buttonBackgroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ButtonLoading(
                  callback: () {
                    final body = {
                      'tableIds': ids,
                    };
                    print('body == $body');
                    if (isSelectedAll) {
                      // context.read<CallButtonCloseOutInactiveCubit>().onCloseOutInactive();
                    } else {
                      // context.read<CallButtonCloseOutInactiveCubit>().onCloseOutInactiveSelected(body);
                    }
                  },
                  isAllowPress: ids.isNotEmpty,
                  isLoading: state.status == CallButtonCloseOutInactiveStatus.Loading,
                  label: l10n.closeSelected,
                  height: 56,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  buttonBackgroundColor: const Color(0xFFFF4E64),
                  labelColor: Colors.white,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildListTables() {
    return BlocBuilder<CallButtonCloseOutInactiveCubit, CallButtonCloseOutInactiveState>(
      builder: (context, state) {
        final tables = state.tablesInactive ?? [];
        final selectedTables = state.slectedIds ?? [];
        // AppLog.e('selectedTables -- ${selectedTables.length}');

        if (tables.isEmpty) {
        // if (tables.isNotEmpty) {
          return const SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Center(
                child: Text(
                  'Không có bàn inactive',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ),
            ),
          );
        }

        return SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverGrid.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: tables.length < 2 ? 1 : 2,
              crossAxisSpacing: 6,
              mainAxisSpacing: 6,
              mainAxisExtent: 80,
            ),
            itemCount: tables.length,
            itemBuilder: (context, index) {
              final isSelected = selectedTables.contains(tables[index].id);
              return _buildTableInactive(
                tables[index],
                isSelected: isSelected,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildSelectAllCheckbox() {
    return BlocBuilder<CallButtonCloseOutInactiveCubit, CallButtonCloseOutInactiveState>(
      builder: (context, state) {        
        return SliverToBoxAdapter(
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
              ),
            ),
            padding: const EdgeInsets.fromLTRB(0, 12, 12, 12),
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: CheckboxListTile(
              title: Transform.translate(
                offset: const Offset(-10, 0),
                child: Text(
                  l10n.selectAllTables,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
              value: state.isSlectedAll,
              tristate: true, // Cho phép trạng thái indeterminate
              onChanged: (value) {
                context.read<CallButtonCloseOutInactiveCubit>().onChangeSlectedAll(value ?? false);
              },
              // Hiển thị trạng thái partially selected
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: const EdgeInsets.only(top: 2, bottom: 2, left: 5),
              checkColor: AppColors.lightPrimaryBackgroundColor,
              activeColor: AppColors.darkPrimaryBackgroundColor,
              checkboxShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: const VisualDensity(horizontal: -4.0, vertical: -4.0),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTableInactive(CallButtonTable table, {bool isSelected = false}) {
    return GestureDetector(
      onTap: () {
        context.read<CallButtonCloseOutInactiveCubit>().onChangeSlectedIds(table.id ?? '');
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.black : const Color(0xFFF5F5F5),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected ? Colors.white : const Color(0xFFF5F5F5),
        ),
        child: Row(
          children: [
            Transform.translate(
              offset: const Offset(-2, 0),
              child: Checkbox(
                value: isSelected,
                onChanged: (value) {
                  context.read<CallButtonCloseOutInactiveCubit>().onChangeSlectedIds(table.id ?? '');
                },
                visualDensity: const VisualDensity(horizontal: -4.0, vertical: -4.0),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                checkColor: AppColors.lightPrimaryBackgroundColor,
                activeColor: AppColors.darkPrimaryBackgroundColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      table.name ?? '',
                      // 'test table name dài thật dài',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Flexible(
                    child: Text(
                      '1h 35p',
                      style: TextStyle(
                        fontSize: 14,
                        color: isSelected ? Colors.red : Colors.black,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmpty(bool iRequest, List<Checkin> checkins, List<Request> orders) {
    return Container(
      alignment: Alignment.center,
      margin: iRequest && checkins.isNotEmpty || iRequest && orders.isNotEmpty ? const EdgeInsets.only(top: 16) : null,
      decoration: BoxDecoration(
          color: Colors.white,
          border: iRequest && checkins.isNotEmpty || orders.isNotEmpty
              ? const Border(
                  top: BorderSide(
                  width: 16,
                  color: Color(0xFFEBEBEB),
                ))
              : null),
      padding: const EdgeInsets.all(32),
      child: Text(
        l10n.tablesEmpty,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildError(String errMsg) {
    return Container(
      color: Colors.white,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 4),
                child: Icon(
                  Icons.warning_rounded,
                  color: Colors.red.shade400,
                ),
              ),
              Text(
                errMsg,
                style: const TextStyle(
                  // fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ButtonControlWidget(
            onPressed: () {
              // context.read<CallButtonDetailCubit>().getTableDetail(widget.table.callButtonId ?? '');
            },
            buttonText: l10n.tryAgain,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ],
      ),
    );
  }

  Widget _buildLoading() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: ListView(
            controller: widget.scrollController,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              SkeletonLoadingWidget(
                itemCount: 5,
                paddingItem: EdgeInsets.zero,
                paddingList: EdgeInsets.zero,
                widget: SkeletonLoadingItem(
                  height: 200,
                  borderRadius: 0,
                ),
              ),
            ],
          ),
        ),
        ColoredBox(
          color: Colors.white,
          child: SkeletonLoadingWidget(
            paddingList: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            paddingItem: EdgeInsets.zero,
            itemCount: 1,
            widget: SkeletonLoadingItem(height: 56),
            isPadding: false,
          ),
        ),
      ],
    );
  }

  Future<void> _onOpenCheckIn(CallButtonTable table, List<Checkin>? checkins, {Checkin? userCheckIn}) async {
    final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;
    await showModalBottomSheet(
      useSafeArea: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: EdgeInsets.only(top: paddingTop),
        padding: MediaQuery.of(context).viewInsets,
        child: DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.7,
          maxChildSize: 1,
          minChildSize: 0.6,
          snap: true,
          snapSizes: const [0.7],
          builder: (context, scrollController) {
            return DecoratedBox(
              decoration: const BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: SafeArea(
                  top: false,
                  bottom: false,
                  child: CallButtonCustomersCheckInPage(
                    scrollController: scrollController,
                    table: table,
                    checkins: checkins ?? table.checkins ?? [],
                    userCheckIn: userCheckIn,
                    isFromDetail: true,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
    // .then((value) => context.read<CallButtonSoundCubit>().onViewDetail(false));
  }

  void _startCountdown(Request item) {
    final itemId = item.id ?? '';
    if (_isClickedMap[itemId] ?? false) return; // Check if countdown has already started
    setState(() {
      _isClickedMap[itemId] = true;
      _remainingSecondsMap[itemId] = 4;
      _timersMap[itemId] = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          if (_remainingSecondsMap[itemId]! > 0) {
            _remainingSecondsMap[itemId] = _remainingSecondsMap[itemId]! - 1;
          } else {
            if (_timersMap[itemId] != null) {
              _timersMap[itemId]?.cancel();
            }
            _removeItem(item);
            context.read<CallButtonDetailCubit>().onSetRequestDone(item.callButtonId ?? '', itemId).then((value) {
              _onUpdateSections(itemId);
            });
          }
        });
      });
    });
  }

  void _processRemovals() {
    while (_itemsToRemove.isNotEmpty) {
      _removeItem(_itemsToRemove.removeAt(0));
    }
  }

  void _removeItem(Request item) {
    final index = _services.indexOf(item);
    if (index != -1) {
      setState(() {
        _timersMap[item.id]?.cancel();
        _timersMap.remove(item.id);
        _isClickedMap.remove(item.id);
        _remainingSecondsMap.remove(item.id);

        _services.removeAt(index);
        _itemsToRemove.remove(item);
        // _listKey.currentState?.removeItem(
        //   index,
        //   (context, animation) => _buildService(item, index, animation), // Thay đổi ở đây
        //   duration: const Duration(milliseconds: 300),
        // );
      });
    }
  }

  void _onUpdateSections(String id) {
    context.read<CallButtonCubit>().onUpdateListTable(id);
  }

  void _onGetDetail(String callButtonId) {
    // if (callButtonId == widget.table.callButtonId) {
    //   context.read<CallButtonDetailCubit>().getTableDetail(
    //         widget.table.callButtonId ?? '',
    //         withLoading: false,
    //       );
    // }
  }

  List<CallButtonTable> _getInactiveTables(List<CallButtonSections> sections) {
    final Set<String> seenIds = {};

    return sections
        .expand((section) => section.tables ?? <CallButtonTable>[])
        .where((table) => table.isInactive == true)
        .where((table) => seenIds.add(table.id ?? '')) // add() returns true if item was added (not duplicate)
        .toList();
  }

  void _buildCompleted() {
    // context.read<CallButtonDetailCubit>().onResetStatus();
    // context.read<CallButtonDetailCubit>().getTableDetail(widget.table.callButtonId ?? '');
    context.read<CallButtonCloseOutInactiveCubit>()
      ..onResetStatus()
      ..onUpdateTablesInactive(
        widget.tables,
        isResetAll: true,
      );
  }
}
