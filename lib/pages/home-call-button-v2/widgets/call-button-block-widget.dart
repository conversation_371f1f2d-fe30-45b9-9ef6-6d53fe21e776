// ignore_for_file: inference_failure_on_function_invocation, empty_catches

import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/call-button-sound/call-button-sound.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home-call-button-v2/widgets/@home-call-button-ver-2-widgets.dart';


class CallButtonBlockWidget extends StatefulWidget {
  const CallButtonBlockWidget({super.key});

  @override
  State<CallButtonBlockWidget> createState() => _CallButtonBlockWidgetState();
}

class _CallButtonBlockWidgetState extends State<CallButtonBlockWidget> with WidgetsBindingObserver {
  ScrollController scrollController = ScrollController();
  late final l10n = context.l10n;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CallButtonCubit, CallButtonState>(
      buildWhen: (previous, current) {
        return !const DeepCollectionEquality().equals(previous.filterSections, current.filterSections);
      },
      builder: (context, state) {
        final allSections = state.filterSections ?? [];
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          itemCount: allSections.length,
          itemBuilder: (context, index) {
            final section = allSections[index];
            final sectionTitle = section.name ?? '';
            final sectionTable = section.tables ?? [];
            final timeCreate = section.createdAt ?? DateTime.now();
            // AppLog.e('sectionTable == ${sectionTable.length}}');
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitle(sectionTitle),
                _buildListTable(sectionTable, timeCreate),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildSectionTitle(String sectionTitle) {
    if (sectionTitle != '') {
      return Padding(
        padding: const EdgeInsets.only(left: 16, top: 16, bottom: 6),
        child: Text(
          sectionTitle.toUpperCase(),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
          ),
        ),
      );
    } else {
      return const SizedBox(height: 16);
    }
  }

  Widget _buildListTable(List<CallButtonTable> tables, DateTime timeCreate) {
    return BlocBuilder<CallButtonCubit, CallButtonState>(
      builder: (context, state) {
        final styleView = state.styleView;
        final filteredTables = context.read<CallButtonCubit>().filterTables(tables);
        AppLog.e('filteredTables == ${filteredTables.length}}');

        if (styleView == CallButtonStyleView.Gridview) {
          return _buildGridviewTable(filteredTables, timeCreate);
        } else {
          return _buildListviewTable(filteredTables, timeCreate);
        }
      },
    );
  }

  Widget _buildGridviewTable(List<CallButtonTable> tables, DateTime timeCreate) {
    return GridView.builder(
      controller: scrollController,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: tables.length < 2 ? 1 : 2,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        mainAxisExtent: 80,
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(
        parent: BouncingScrollPhysics(),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: tables.length,
      itemBuilder: (context, index) {
        return _buildTableWithBadge(tables[index], timeCreate);
      },
    );
  }

  Widget _buildListviewTable(List<CallButtonTable> tables, DateTime timeCreate) {
    return ListView.builder(
      controller: scrollController,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(
        parent: BouncingScrollPhysics(),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: tables.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(top: index > 0 ? 6 : 0),
          child: _buildTableWithBadge(tables[index], timeCreate),
        );
      },
    );
  }

  Color getStatusColor(DateTime timeCreate) {
      final auth = context.read<AuthBloc>().state;
      final timezone = auth.businessTimeZone ?? '';
      final settings = auth.business?.cbTableStatusSettings;
      
      // Tính thời gian chênh lệch (phút)
      // final now = DateTime.now();
      // final now = DateTimeHelper.currentTime(timezone);
      // final tableTimeCreate = DateTimeHelper.formatTime(
      //     timeCreate,
      //     timezone,
      //   );
      final now = timezone != null 
    ? DateTimeHelper.currentTime(timezone)
    : DateTime.now();

// Convert timeCreate từ UTC sang timezone để so sánh chính xác
final timeCreateInTimezone = timezone != null
    ? DateTimeHelper.currentTime(timezone, time: timeCreate.toUtc(), isUTC: false)
    : timeCreate;
      final differenceInMinutes = now.difference(timeCreateInTimezone).inMinutes;

      AppLog.e('now = $now // timeCreate == $timeCreate // timeCreateInTimezone == $timeCreateInTimezone');
      AppLog.e('differenceInMinutes == $differenceInMinutes');
      
      // Lấy settings
      final enableInProgress = settings?.enableInProgress ?? false;
      final enableAttentionRequired = settings?.enableAttentionRequired ?? false;
      final inProgressChangeAfterMinutes = settings?.inProgressChangeAfterMinutes ?? 0;
      final attentionRequiredChangeAfterMinutes = settings?.attentionRequiredChangeAfterMinutes ?? 0;
      
      // Logic xác định màu
      if (enableInProgress && enableAttentionRequired) {
        if (differenceInMinutes < inProgressChangeAfterMinutes) {
          return Colors.lightBlue.shade200; // Xanh
        } else if (differenceInMinutes < attentionRequiredChangeAfterMinutes) {
          return Colors.yellow.shade200; // Vàng
        } else {
          return Colors.red.shade200; // Đỏ
        }
      } else if (enableInProgress && !enableAttentionRequired) {
        return differenceInMinutes < inProgressChangeAfterMinutes 
            ? Colors.lightBlue.shade200 
            : Colors.yellow.shade200;
      } else if (!enableInProgress && enableAttentionRequired) {
        return differenceInMinutes < attentionRequiredChangeAfterMinutes 
            ? Colors.lightBlue.shade200 
            : Colors.red.shade200;
      } else {
        return Colors.lightBlue.shade200; // Mặc định
      }
    }

  Widget _buildTableWithBadge(CallButtonTable table, DateTime timeCreate) {
    final checkins = table.checkins ?? [];
    // final filteredList = checkins.where((checkin) => !(checkin.isDoneAdditionalPoints ?? false)).toList();
    final userCheckins = context.read<CallButtonCubit>().filterUserCheckin(checkins);
    final qtyCheckin = userCheckins.length;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        _buildTable(table, timeCreate),
        if (qtyCheckin > 1)
          Positioned(
            top: -6,
            right: -4,
            child: InkWell(
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
                _onOpenCheckIn(table);
              },
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xFFFF4E64),
                ),
                child: Center(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Padding(
                      padding: const EdgeInsets.all(2),
                      child: Text(
                        '${qtyCheckin > 9 ? '9+' : qtyCheckin}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTable(CallButtonTable table, DateTime timeCreate) {
    final request = table.requests ?? [];
    // final icons = context.read<CallButtonCubit>().findAndAddIcons(request);
    final checkins = table.checkins ?? [];
    // final userCheckins = context.read<CallButtonCubit>().filterUserCheckin(checkins);
    final tableSttColor = getStatusColor(timeCreate);

    return BlocBuilder<CallButtonCubit, CallButtonState>(
      builder: (context, state) {
        final icons = state.findAndAddIcons(request);
        final userCheckins = state.filterUserCheckin(checkins);

        return InkWell(
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
            context.read<CallButtonSoundCubit>().onViewDetail(true);
            _onOpenTable(table);
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            constraints: const BoxConstraints(minHeight: 75),
            decoration: BoxDecoration(
              border: Border.all(
                width: 1,
                color: const Color(0xFFE2E2E2),
              ),
              borderRadius: BorderRadius.circular(12),
              // color: const Color(0xFF3448F0).withValues(alpha: .06),
              color: tableSttColor,
            ),
            padding: const EdgeInsets.fromLTRB(8, 6, 8, 6),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: Text(
                        table.name ?? '',
                        // 'test cai ten ban dai vkl dai',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        softWrap: true,
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ),
                    if (userCheckins.isNotEmpty)
                      InkWell(
                        onTap: () {
                          context.read<CallButtonSoundCubit>().onViewDetail(true);
                          _onOpenCheckIn(table);
                        },
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        child: Transform.translate(
                          offset: const Offset(2, -5),
                          child: SvgPicture.asset(
                            'assets/svgs/checkin.svg',
                            width: 30,
                            height: 30,
                          ),
                        ),
                      ),
                  ],
                ),
                _buildListIcons(icons, request.length),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildListIcons(List<String> icons, int qtyRequest) {
    return Padding(
      padding: const EdgeInsets.only(top: 6.0),
      child: Row(
        children: [
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              physics: const AlwaysScrollableScrollPhysics(
                parent: BouncingScrollPhysics(),
              ),
              padding: EdgeInsets.zero,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: icons.map((icon) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 4),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(3),
                          child: SvgPicture.asset(
                            'assets/svgs/call-button/all/$icon',
                            width: 20,
                            height: 20,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          if (qtyRequest > 0)
            Container(
              width: 22,
              height: 22,
              margin: const EdgeInsets.only(left: 6),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(6)),
                color: Color(0xFFC5D8FD),
              ),
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Padding(
                    padding: const EdgeInsets.all(1),
                    child: Text(
                      '$qtyRequest',
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _onOpenTable(CallButtonTable table) async {
    if (!mounted) return;
    try {
      final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;
      await showModalBottomSheet(
        useSafeArea: true,
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) => Container(
          margin: EdgeInsets.only(top: paddingTop),
          padding: MediaQuery.of(context).viewInsets,
          child: DraggableScrollableSheet(
            expand: false,
            initialChildSize: 0.7,
            maxChildSize: 1,
            builder: (BuildContext context, ScrollController scrollController) {
              return DecoratedBox(
                decoration: const BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: GestureDetector(
                  onTap: () => FocusScope.of(context).unfocus(),
                  child: SafeArea(
                    top: false,
                    bottom: false,
                    child: CallButtonTableDetail(
                      scrollController: scrollController,
                      table: table,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );
      if (mounted) {
        context.read<CallButtonSoundCubit>().onViewDetail(false);
      }
    } catch (e) {}
  }

  Future<void> _onOpenCheckIn(CallButtonTable table) async {
    if (!mounted) return;
    try {
      final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;
      await showModalBottomSheet(
        useSafeArea: true,
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) => Container(
          margin: EdgeInsets.only(top: paddingTop),
          padding: MediaQuery.of(context).viewInsets,
          child: DraggableScrollableSheet(
            expand: false,
            initialChildSize: 0.7,
            maxChildSize: 1,
            builder: (BuildContext context, ScrollController scrollController) {
              return DecoratedBox(
                decoration: const BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: GestureDetector(
                  onTap: () => FocusScope.of(context).unfocus(),
                  child: SafeArea(
                    top: false,
                    bottom: false,
                    child: CallButtonCustomersCheckInPage(
                      scrollController: scrollController,
                      table: table,
                      checkins: context.read<CallButtonCubit>().filterUserCheckin(table.checkins ?? []),
                      onChanged: (value) {
                        if (value == 'reload' && mounted) {
                          context.read<CallButtonCubit>().getSections(withLoading: false);
                        }
                      },
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );
      if (mounted) {
        context.read<CallButtonSoundCubit>().onViewDetail(false);
      }
    } catch (e) {}
  }

  void _buildCompleted() {}
}
