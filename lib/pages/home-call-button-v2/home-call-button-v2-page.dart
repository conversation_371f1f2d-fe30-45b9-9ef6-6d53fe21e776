// ignore_for_file: strict_raw_type, unawaited_futures, use_build_context_synchronously, empty_catches, flutter_style_todos, use_decorated_box, inference_failure_on_function_invocation

import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/call-button-sections.model.dart';
import 'package:stickyqrbusiness/@core/models/section-table-detail.model.dart';
import 'package:stickyqrbusiness/@core/store/call-button-close-out-inactive/call-button-close-out-inactive.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-count-down-request-orders/call-button-count-down-request-orders.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-count-down-request/call-button-count-down-request.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-detail/call-button-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-theme/home-theme.cubit.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/home-define/define-config.model.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/home-define/define-theme.dart';
import 'package:stickyqrbusiness/@widgets/skeleton-loading-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home-call-button-v2/widgets/@home-call-button-ver-2-widgets.dart';

class HomeCallButtonPageVer2 extends StatelessWidget {
  const HomeCallButtonPageVer2({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CallButtonCloseOutInactiveCubit(),
      child: const HomeCallButtonPageVer2View(),
    );
  }
}

class HomeCallButtonPageVer2View extends StatefulWidget {
  const HomeCallButtonPageVer2View({super.key});

  @override
  State<HomeCallButtonPageVer2View> createState() => _HomeCallButtonPageVer2ViewState();
}

class _HomeCallButtonPageVer2ViewState extends State<HomeCallButtonPageVer2View> with WidgetsBindingObserver, TickerProviderStateMixin {
  final _expansionController = ExpansionTileController();
  late AppLocalizations l10n = context.l10n;
  StreamSubscription? _subscription;

  final List<Request> _itemsToRemove = [];
  final Map<String, bool> _isClickedMap = {};
  final Map<String, int> _remainingSecondsMap = {};
  final Map<String, Timer?> _timersMap = {};

  final List<CallButtonRequestProduct> _productsToRemove = [];
  final Map<String, bool> _isClickedMapProduct = {};
  final Map<String, int> _remainingSecondsMapProduct = {};
  final Map<String, Timer?> _timersMapProducts = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
    _subscription?.cancel();
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    l10n = AppLocalizations.of(context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final Business? business = state.business;
        final acceptCallButton = business?.enableCallButton ?? false;
        if (!acceptCallButton) {
          return const SizedBox.shrink();
        } else {

          return MultiBlocListener(
            listeners: [
              BlocListener<CallButtonCountdownRequestCubit, CallButtonCountDownRequestState>(
                listener: (context, state) {
                  if (state.status == CallButtonCountdownRequestStatus.Success) {
                    _startCountdown(state.item ?? Request());
                  }
                },
              ),
              BlocListener<CallButtonCountdownRequestOrdersCubit, CallButtonCountDownRequestOrdersState>(
                listener: (context, state) {
                  if (state.status == CallButtonCountdownRequestOrdersStatus.Success) {
                    _startCountdownProduct(state.request ?? Request(), state.product ?? CallButtonRequestProduct());
                  }
                },
              ),
            ],
            child: _buildContent(),
          );
        }
      },
    );
  }

  Widget _buildContent() {
    return BlocBuilder<CallButtonCubit, CallButtonState>(
      builder: (context, state) {
        final status = state.status;
        switch (status) {
          case CallButtonStatus.Loading:
            return _buildLoading();
          case CallButtonStatus.Edit:
          case CallButtonStatus.Progress:
          // case CallButtonStatus.Initial:
          case CallButtonStatus.Success:
          case CallButtonStatus.Error:
            return buildBody();
          default:
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget buildBody() {
    return BlocBuilder<HomeThemeCubit, HomeThemesState>(
      builder: (context, themeState) {
        final theme = DefineHomeThemeConfig.getHomeTheme(themeState.themeName);
        return BlocBuilder<CallButtonCubit, CallButtonState>(
          builder: (context, state) {
            final allSections = state.allSections ?? [];
            final isEmptyRequest = hasActiveTableWithRequests(allSections);
            final bool isShowCloseOutInactive = allSections.any((section) => section.tables!.any((table) => table.isInactive != null && table.isInactive == true));
            return allSections.isNotEmpty
                ? Container(
                    decoration: isShowCloseOutInactive || isEmptyRequest
                        ? BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                          )
                        : null,
                    // margin: isShowCloseOutInactive || isEmptyRequest ? EdgeInsets.only(top: theme.marginContent) : null,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Stack(
                            // fit: StackFit.loose,
                            children: [
                              if (isShowCloseOutInactive || isEmptyRequest) ...{
                                _buildBlockCallbutton(),
                                _buildHeader(theme),
                              } else ...{
                                _buildEmpty(theme),
                              }
                            ],
                          ),
                        ),
                        // _buildExpandedButton(),
                        _buildBlocInactive(allSections),
                      ],
                    ),
                  )
                : _buildEmpty(theme);
          },
        );
      },
    );
  }

  Widget _buildBlocInactive(List<CallButtonSections> allSections) {
    final tablesInactive = _getInactiveTables(allSections);
    final countTables = tablesInactive.length;

    return Container(
      margin: const EdgeInsets.only(top: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          bottomRight: Radius.circular(12),
          bottomLeft: Radius.circular(12),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/svgs/waring.svg',
              width: 18,
              height: 18,
              colorFilter: const ColorFilter.mode(
                Color(0xFFD33030),
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                '${l10n.inactiveTables} ($countTables)',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ),

            GestureDetector(
              onTap: () {
                _onOpenCloseOutWidget(tablesInactive);
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    l10n.view,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF3448F0),
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Icon(
                    Icons.arrow_forward,
                    color: Color(0xFF3448F0),
                    size: 20,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBlockCallbutton() {
    return BlocBuilder<CallButtonCubit, CallButtonState>(
      builder: (context, state) {
        final allSections = state.allSections ?? [];
        final isExpand = state.isExpanded;
        final isEmptyRequest = hasActiveTableWithRequests(allSections);
        return ExpansionTile(
          controller: _expansionController,
          onExpansionChanged: (bool expanded) {},
          initiallyExpanded: isExpand,
          backgroundColor: Colors.transparent,
          shape: const Border(),
          title: GestureDetector(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: const SizedBox.shrink(),
          ),
          // trailing: const SizedBox.shrink(),
          trailing: Builder(
            builder: (context) {
              return const SizedBox.shrink();
            },
          ),
          children: <Widget>[
            if (isEmptyRequest) const CallButtonBlockWidget(),
            // _buildInactiveTables(),
          ],
        );
      },
    );
  }

  Widget _buildHeader(AppHomeThemeModel theme) {
    return BlocBuilder<CallButtonCubit, CallButtonState>(
      builder: (context, state) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.transparent,
            border: Border(
                bottom: BorderSide(
              width: 1,
              color: Color(0xFFEBEBEB),
            )),
          ),
          padding: const EdgeInsets.fromLTRB(14, 8, 0, 8),
          height: 58,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: SvgPicture.asset(
                        'assets/svgs/call-button.svg',
                        colorFilter: const ColorFilter.mode(
                          AppColors.iconColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Flexible(
                      child: Text(
                        l10n.homeCallButton,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        softWrap: true,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.appBlackColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // BlocBuilder<CallButtonCubit, CallButtonState>(
              //   builder: (context, state) {
              //     final styleView = state.styleView;
              //     final styleIcon = styleView == CallButtonStyleView.Gridview ? 'grid' : 'bullet-list';
              //     final styleChange = styleView == CallButtonStyleView.Gridview ? CallButtonStyleView.Listview : CallButtonStyleView.Gridview;
              //     final isShowBtn = _isShowStyleViewBtn(state.allSections ?? []);
              //     return isShowBtn
              //         ? GestureDetector(
              //             onTap: () {
              //               context.read<CallButtonCubit>().onChangeStyleView(styleChange);
              //             },
              //             child: Container(
              //               height: 28,
              //               width: 48,
              //               padding: const EdgeInsets.all(2),
              //               margin: const EdgeInsets.only(left: 12),
              //               decoration: BoxDecoration(
              //                 color: AppColors.appBlueColor.withValues(alpha: .12),
              //                 borderRadius: BorderRadius.circular(16),
              //               ),
              //               child: SvgPicture.asset('assets/svgs/$styleIcon.svg'),
              //             ),
              //           )
              //         : const SizedBox.shrink();
              //   },
              // ),
              BlocBuilder<CallButtonCubit, CallButtonState>(
                builder: (context, state) {
                  final styleView = state.styleView;
                  final isGridView = styleView == CallButtonStyleView.Gridview;
                  final styleChange = isGridView ? CallButtonStyleView.Listview : CallButtonStyleView.Gridview;
                  final isShowBtn = _isShowStyleViewBtn(state.allSections ?? []);
                  
                  return isShowBtn
                    ? GestureDetector(
                        onTap: () {
                          context.read<CallButtonCubit>().onChangeStyleView(styleChange);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF6F8FA),
                            borderRadius: BorderRadius.circular(8),
                            // boxShadow: [
                            //   BoxShadow(
                            //     color: Colors.black.withValues(alpha: 0.1),
                            //     blurRadius: 8,
                            //     offset: const Offset(0, 2),
                            //   ),
                            // ],
                          ),
                          child: Row(
                            children: [
                              // Grid View Icon
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: isGridView ? Colors.white : Colors.transparent,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Center(
                                  child: SvgPicture.asset(
                                    'assets/svgs/grid.svg',
                                    width: 16,
                                    height: 16,
                                    colorFilter: ColorFilter.mode(
                                      isGridView ? Colors.black : const Color(0xFF999CA0),
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              // List View Icon
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: !isGridView ? Colors.white : Colors.transparent,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Center(
                                  child: SvgPicture.asset(
                                    'assets/svgs/bullet-list.svg',
                                    width: 16,
                                    height: 16,
                                    colorFilter: ColorFilter.mode(
                                      !isGridView ? Colors.black : const Color(0xFF999CA0),
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : const SizedBox.shrink();
                },
              ),
              IconButton(
                icon: SvgPicture.asset(
                  'assets/svgs/setting.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.appBlackColor,
                    BlendMode.srcIn,
                  ),
                ),
                onPressed: () => AppBased.go(context, AppRoutes.callButtonPage),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInactiveTables() {
    return BlocBuilder<CallButtonCubit, CallButtonState>(
      builder: (context, state) {
        final allSections = state.allSections ?? [];
        // AppLog.d('allSections == ${jsonEncode(allSections)}');
        final bool isShowCloseOutInactive = allSections.any((section) => section.tables!.any((table) => table.isInactive != null && table.isInactive == true));
        final tablesName = _getTableNames(allSections);
        final textButton = tablesName != '' ? '${l10n.homeCallBtnCloseOutInactiveTable}: ' : l10n.homeCallBtnCloseOutInactiveTable;
        final tablesInactive = _getInactiveTables(allSections);
        // AppLog.d('tablesInactive = ${jsonEncode(tablesInactive)}');
        return isShowCloseOutInactive
            ? BlocConsumer<CallButtonCloseOutInactiveCubit, CallButtonCloseOutInactiveState>(
                listener: (context, state) {
                  if (state.status == CallButtonCloseOutInactiveStatus.Success) {
                    context.read<CallButtonCubit>().getSections(withLoading: false);
                  }
                },
                builder: (context, state) {
                  final status = state.status;
                  return Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                    child: ElevatedButton(
                      // onPressed:
                      //     status == CallButtonCloseOutInactiveStatus.Loading
                      //         ? null
                      //         : () => context
                      //             .read<CallButtonCloseOutInactiveCubit>()
                      //             .onCloseOutInactive(),
                      onPressed: () {
                        print('open close out');
                        _onOpenCloseOutWidget(tablesInactive);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF595D62),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: Center(
                                child: RichText(
                                  textAlign: TextAlign.center,
                                  text: TextSpan(
                                    text: textButton,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    children: [
                                      TextSpan(
                                        text: tablesName,
                                        style: const TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.normal,
                                          fontStyle: FontStyle.italic,
                                          color: Color(0xFFE2E2E2),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            if (status == CallButtonCloseOutInactiveStatus.Loading)
                              Container(
                                width: 16,
                                height: 16,
                                padding: const EdgeInsets.all(2.0),
                                margin: const EdgeInsets.only(left: 6.0),
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )
            : const SizedBox.shrink();
      },
    );
  }

  Widget _buildExpandedButton() {
    return BlocBuilder<CallButtonCubit, CallButtonState>(
      builder: (context, state) {
        final isExpand = state.isExpanded;
        final allSections = state.allSections ?? [];
        final isShowEpanded = hasActiveTableWithRequests(allSections);
        // final tableQty = _countTables(allSections);
        // return tableQty > 0
        return isShowEpanded
            ? Flexible(
                child: Container(
                  height: 32,
                  width: 72,
                  margin: const EdgeInsets.only(bottom: 16, top: 8),
                  decoration: BoxDecoration(
                    color: AppColors.appBGAvatarColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  alignment: Alignment.center,
                  child: ElevatedButton(
                    onPressed: _toggleExpansion,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      fixedSize: const Size(72, 32),
                    ),
                    child: SvgPicture.asset(
                      isExpand ? 'assets/svgs/arrow-up-icon.svg' : 'assets/svgs/arrow-down-icon.svg',
                      colorFilter: const ColorFilter.mode(
                        AppColors.darkPrimaryBackgroundColor,
                        BlendMode.srcIn,
                      ),
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
              )
            : const SizedBox(height: 16);
      },
    );
  }

  Widget _buildLoading() {
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        SkeletonLoadingWidget(
          itemCount: 1,
          paddingItem: EdgeInsets.zero,
          paddingList: const EdgeInsets.only(top: 12),
          widget: SkeletonLoadingItem(height: 80),
        ),
      ],
    );
  }

  Widget _buildEmpty(AppHomeThemeModel theme) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.lightPrimaryBackgroundColor,
        border: Border.all(
          width: .5,
          color: AppColors.appBorderColor,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: SvgPicture.asset(
                        'assets/svgs/call-button.svg',
                        colorFilter: const ColorFilter.mode(
                          AppColors.iconColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Text(
                      l10n.homeCallButton,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.appBlackColor,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: SvgPicture.asset(
                  'assets/svgs/setting.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.appBlackColor,
                    BlendMode.srcIn,
                  ),
                ),
                onPressed: () => AppBased.go(context, AppRoutes.callButtonPage),
                padding: const EdgeInsets.only(right: 8.0),
              ),
            ],
          ),
          Container(
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(
                  width: 1,
                  color: Color(0xFFEBEBEB),
                ),
              ),
            ),
            padding: const EdgeInsets.all(16),
            child: Text(
              l10n.homeCallBtnThereAreNoRequestPendingAtThisTime,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColors.appLabelTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleExpansion() {
    if (_expansionController.isExpanded) {
      _expansionController.collapse();
    } else {
      _expansionController.expand();
    }
    context.read<CallButtonCubit>().onChangeExpand(_expansionController.isExpanded);
  }

  bool _isShowStyleViewBtn(List<CallButtonSections> data) {
    for (final blockItem in data) {
      int countValidTables = 0; // Đếm số bảng thỏa mãn điều kiện trong mỗi blockItem
      if (blockItem.tables != null) {
        for (final table in blockItem.tables!) {
          if (table.isInactive == false && table.requests != null && table.requests!.isNotEmpty) {
            countValidTables++;
          }
          if (countValidTables > 1) {
            return true;
          }
        }
      }
    }
    return false;
  }

  String _getTableNames(List<CallButtonSections> sections) {
    final Set<String> existingTableIds = {};
    final List<String> tableNames = [];

    for (final section in sections) {
      for (final CallButtonTable table in section.tables ?? []) {
        if (table.isInactive != null && table.isInactive == true) {
          if (!existingTableIds.contains(table.id)) {
            existingTableIds.add(table.id ?? '');
            tableNames.add(table.name ?? '');
          }
        }
      }
    }
    return tableNames.join(', ');
  }

  List<CallButtonTable> _getInactiveTables(List<CallButtonSections> sections) {
    final Set<String> seenIds = {};

    return sections
        .expand((section) => section.tables ?? <CallButtonTable>[])
        .where((table) => table.isInactive == true)
        .where((table) => seenIds.add(table.id ?? '')) // add() returns true if item was added (not duplicate)
        .toList();
  }

  bool hasTableWithRequests(List<CallButtonSections> sections) {
    for (final section in sections) {
      for (final CallButtonTable table in section.tables ?? []) {
        if (table.requests != null && table.requests!.isNotEmpty) {
          return true; // Nếu có table có requests, trả về true
        }
      }
    }
    return false; // Nếu không tìm thấy table nào có requests, trả về false
  }

  bool hasActiveTableWithRequests(List<CallButtonSections>? allSections) {
    if (allSections == null || allSections.isEmpty) {
      return false;
    }
    for (final section in allSections) {
      for (final CallButtonTable table in section.tables ?? []) {
        // if (table.requests != null && table.requests!.isNotEmpty && table.isInactive == false) {
        if (table.isInactive == false) {
          return true;
        }
      }
    }
    return false;
  }

  void _startCountdown(Request item) {
    if (_isClickedMap[item.id] ?? false) {
      // nếu item đã tồn tại, dừng đếm ngược & xóa khỏi list
      setState(() {
        _timersMap[item.id]?.cancel();
        _timersMap.remove(item.id);
        _remainingSecondsMap.remove(item.id);
        _isClickedMap.remove(item.id);
        _removeItem(item);
        // print('Item ${item.id} has been removed');
      });
    } else {
      // nếu chưa có, bắt đầu đếm ngược
      setState(() {
        _isClickedMap[item.id ?? ''] = true;
        _remainingSecondsMap[item.id ?? ''] = 4;
        _timersMap[item.id ?? ''] = Timer.periodic(const Duration(seconds: 1), (timer) {
          setState(() {
            if (_remainingSecondsMap[item.id]! > 0) {
              _remainingSecondsMap[item.id ?? ''] = _remainingSecondsMap[item.id]! - 1;
            } else {
              // print('delete item = ${item.id}');
              _timersMap[item.id]?.cancel();
              _timersMap.remove(item.id);
              _remainingSecondsMap.remove(item.id);
              _isClickedMap.remove(item.id);
              _removeItem(item);
              context.read<CallButtonDetailCubit>().onSetRequestDone(item.callButtonId ?? '', item.id ?? '').then((value) {
                _onUpdateSections(item.id ?? '');
              });
            }
          });
        });
      });
    }
  }

  void _removeItem(Request item) {
    setState(() {
      _timersMap[item.id]?.cancel();
      _timersMap.remove(item.id);
      _isClickedMap.remove(item.id);
      _remainingSecondsMap.remove(item.id);
      _itemsToRemove.remove(item);
    });
  }

  void _onUpdateSections(String id) {
    context.read<CallButtonCubit>().onUpdateListTable(id);
  }

  void _startCountdownProduct(Request request, CallButtonRequestProduct item) {
    if (_isClickedMapProduct[item.id] ?? false) {
      // nếu item đã tồn tại, dừng đếm ngược & xóa khỏi list
      setState(() {
        _timersMapProducts[item.id]?.cancel();
        _timersMapProducts.remove(item.id);
        _remainingSecondsMapProduct.remove(item.id);
        _isClickedMapProduct.remove(item.id);
        _removeItemProduct(item);
      });
    } else {
      // nếu chưa có, bắt đầu đếm ngược
      setState(() {
        _isClickedMapProduct[item.id ?? ''] = true;
        _remainingSecondsMapProduct[item.id ?? ''] = 4;
        _timersMapProducts[item.id ?? ''] = Timer.periodic(const Duration(seconds: 1), (timer) {
          setState(() {
            if (_remainingSecondsMapProduct[item.id]! > 0) {
              _remainingSecondsMapProduct[item.id ?? ''] = _remainingSecondsMapProduct[item.id]! - 1;
            } else {
              _timersMapProducts[item.id]?.cancel();
              _timersMapProducts.remove(item.id);
              _remainingSecondsMapProduct.remove(item.id);
              _isClickedMapProduct.remove(item.id);
              _removeItemProduct(item);
              context.read<CallButtonDetailCubit>().onSetRequestDone(request.callButtonId ?? '', request.id ?? '', itemId: item.id ?? '').then((value) {
                _onUpdateSections(item.id ?? '');
              });
            }
          });
        });
      });
    }
  }

  void _removeItemProduct(CallButtonRequestProduct item) {
    setState(() {
      _timersMapProducts[item.id]?.cancel();
      _timersMapProducts.remove(item.id);
      _isClickedMapProduct.remove(item.id);
      _remainingSecondsMapProduct.remove(item.id);
      _productsToRemove.remove(item);
    });
  }

  String setAssetSourceBeep(int name) {
    switch (name) {
      case 1:
        return 'sounds/tink.mp3';
      case 2:
        return 'sounds/pop.mp3';
      default:
        return 'sounds/beep.mp3';
    }
  }

  Future<void> _onOpenCloseOutWidget(List<CallButtonTable> tables) async {
    if (!mounted) return;
    try {
      final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;
      await showModalBottomSheet(
        useSafeArea: true,
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) => Container(
          margin: EdgeInsets.only(top: paddingTop),
          padding: MediaQuery.of(context).viewInsets,
          child: DraggableScrollableSheet(
            expand: false,
            initialChildSize: 0.7,
            maxChildSize: 1,
            builder: (BuildContext context, ScrollController scrollController) {
              return DecoratedBox(
                decoration: const BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: GestureDetector(
                  onTap: () => FocusScope.of(context).unfocus(),
                  child: SafeArea(
                    top: false,
                    bottom: false,
                    child: CallButtonCloseOutTableWidget(
                      scrollController: scrollController,
                      tables: tables,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );
      if (mounted) {
        // context.read<CallButtonSoundCubit>().onViewDetail(false);
      }
    } catch (e) {}
  }

  void _buildCompleted() {}
}
