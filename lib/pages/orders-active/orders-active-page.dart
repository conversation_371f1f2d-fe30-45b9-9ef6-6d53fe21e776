// ignore_for_file: empty_catches

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/order-active-change-tab/order-active-change-tab.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-new-notification/order-new-notification.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-active-reload/ordering-active-reload.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/custom-bubble.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/@orders-active-widget.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';

class OrdersActivePage extends StatefulWidget {
  const OrdersActivePage({super.key});

  @override
  State<OrdersActivePage> createState() => _OrdersActivePageState();
}

class _OrdersActivePageState extends State<OrdersActivePage> with WidgetsBindingObserver {
  OrderActiveChangeTabCubit? orderActiveChangeTabCubit;
  @override
  void initState() {
    context.read<OrderNewNotificationCubit>().onClosePage();
    context.read<OrdersActiveCubit>().getOrdersActive();

    orderActiveChangeTabCubit = context.read<OrderActiveChangeTabCubit>();
    orderActiveChangeTabCubit?.onChangedClose(OrderActiveOpen.Open);
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _onBuildCompleted());
    super.initState();
  }

  @override
  void dispose() {
    orderActiveChangeTabCubit?.onChangedClose(OrderActiveOpen.Close);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    int index = 1;
    String? idOrder;
    if (args != null && args is ScreenArguments) {
      idOrder = args.type;
      index = 1;
    } else {}
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (value, f) {
          return;
        },
        child: OrderActiveContent(index: index, idOrder: idOrder, isHomeWidget: false));
  }

  void _onBuildCompleted() {}
}

class OrderActiveContent extends StatefulWidget {
  final int index;
  final String? idOrder;
  final bool safeAreaButtom;
  final bool isShowMenu;
  final bool isHomeWidget;
  final bool isHorizontal;

  const OrderActiveContent({
    Key? key,
    this.index = 0,
    this.idOrder,
    this.safeAreaButtom = true,
    this.isShowMenu = true,
    this.isHomeWidget = false,
    this.isHorizontal = true,
  }) : super(key: key);

  @override
  State<OrderActiveContent> createState() => _OrderActiveContentState();
}

class _OrderActiveContentState extends State<OrderActiveContent> with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  Timer? _timerActiveOrders;
  late TabController _tabController = TabController(length: 5, vsync: this);
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int _index = 1;
  String? _idOrder;

  @override
  void initState() {
    try {
      _index = widget.index;
      _idOrder = widget.idOrder;
      try {
        _tabController = TabController(length: 5, initialIndex: _index, vsync: this);
      } catch (e) {
        _tabController = TabController(length: 5, initialIndex: _index, vsync: this);
      }

      Future.delayed(const Duration(milliseconds: 1000), () {
        if (_idOrder != null && _idOrder != '') {
          AppBased.goToOrderDetailPage(
            context,
            _idOrder ?? '',
            onChanged: (value) {
              context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
            },
          );
        }
      });
    } catch (e) {}
    super.initState();
  }

  @override
  void dispose() {
    try {
      _timerActiveOrders?.cancel();
      _tabController.dispose();
    } catch (e) {}
    super.dispose();
  }

  void timerSartCalButton() {
    // try {
    //   _timerActiveOrders?.cancel();
    //   _timerActiveOrders = null;
    //   _timerActiveOrders = Timer.periodic(const Duration(seconds: 30), (timer) {
    //     context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
    //   });
    // } catch (e) {
    //   AppLog.e('timerSart error: $e');
    //   _timerActiveOrders?.cancel();
    //   _timerActiveOrders = null;
    //   _timerActiveOrders = Timer.periodic(const Duration(seconds: 30), (timer) {
    //     context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
    //   });
    // }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final width = MediaQuery.of(context).size.width;
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: AppColors.appTransparentColor,
      ),
      child: Scaffold(
        key: _scaffoldKey,
        backgroundColor: AppColors.appTransparentColor,
        endDrawer: widget.isShowMenu
            ? OrdersActiveDrawerWidget(
                activePage: OrderDrawerPageActive.ActiveOrders,
                isFromHome: widget.isHomeWidget,
              )
            : null,
        appBar: AppBar(
          backgroundColor: AppColors.appBlackColor,
          automaticallyImplyLeading: false,
          toolbarHeight: 0,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(56),
            child: Container(
              margin: EdgeInsets.only(right: widget.isShowMenu ? 12 : 0),
              alignment: Alignment.center,
              height: 48,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Flexible(
                    child: Center(
                      child: BlocBuilder<OrdersActiveCubit, OrdersActiveState>(
                        builder: (context, state) {
                          return BlocListener<OrderActiveChangeTabCubit, OrderActiveChangeTabState>(
                            listener: (context, state) {
                              switch (state.tab) {
                                case OrderActiveChangeTabStatus.All:
                                  _changeTab(0);
                                  return;
                                case OrderActiveChangeTabStatus.NeedsAction:
                                  _changeTab(1);
                                  return;
                                case OrderActiveChangeTabStatus.InProgress:
                                  _changeTab(2);
                                  return;
                                case OrderActiveChangeTabStatus.Ready:
                                  _changeTab(3);
                                  return;
                                case OrderActiveChangeTabStatus.Scheduled:
                                  _changeTab(4);
                                  return;
                                default:
                                  return;
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              margin: const EdgeInsets.only(right: 16),
                              decoration: BoxDecoration(
                                color: AppColors.tabsBGColor,
                                borderRadius: BorderRadius.circular(99),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(99),
                                child: TabBar(
                                  physics: const ClampingScrollPhysics(),
                                  automaticIndicatorColorAdjustment: false,
                                  isScrollable: width < 880,
                                  labelPadding: width < 880 ? null : EdgeInsets.zero,
                                  tabAlignment: width < 880 ? TabAlignment.center : TabAlignment.fill,
                                  padding: EdgeInsets.zero,
                                  indicatorPadding: EdgeInsets.zero,
                                  indicatorSize: TabBarIndicatorSize.tab,
                                  dividerHeight: 0,
                                  controller: _tabController,
                                  indicatorWeight: 0,
                                  splashBorderRadius: BorderRadius.circular(99),
                                  indicator: BoxDecoration(
                                    color: AppColors.lightPrimaryBackgroundColor,
                                    borderRadius: BorderRadius.circular(99),
                                    border: Border.all(color: AppColors.lightPrimaryBackgroundColor),
                                  ),
                                  labelColor: AppColors.appBlackColor,
                                  unselectedLabelColor: AppColors.lightPrimaryBackgroundColor,
                                  labelStyle: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                  unselectedLabelStyle: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                  tabs: [
                                    OrderActiveTab(label: l10n.all, count: state.allOrders?.length ?? 0),
                                    OrderActiveTab(label: l10n.needsAction, count: state.newOrders?.length ?? 0),
                                    OrderActiveTab(label: l10n.inProgressOrder, count: state.inprogresOrders?.length ?? 0),
                                    OrderActiveTab(label: l10n.readyOrder, count: state.readyOrders?.length ?? 0),
                                    OrderActiveTab(label: l10n.scheduledOrder, count: state.scheduledOrders?.length ?? 0),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  if (widget.isShowMenu)
                    IconButton(
                      splashRadius: 16,
                      style: IconButton.styleFrom(
                        backgroundColor: AppColors.tabsBGColor,
                      ),
                      icon: SvgPicture.asset(
                        'assets/svgs/setting.svg',
                        // 'assets/svgs/menu.svg',
                        colorFilter: const ColorFilter.mode(
                          AppColors.lightPrimaryBackgroundColor,
                          BlendMode.srcIn,
                        ),
                        width: 24,
                        height: 24,
                      ),
                      onPressed: () => _scaffoldKey.currentState?.openEndDrawer(),
                    ),
                ],
              ),
            ),
          ),
        ),
        body: DecoratedBox(
          decoration: const BoxDecoration(
            color: AppColors.appBlackColor,
          ),
          child: BlocListener<OrderingActiveReloadCubit, OrderingActiveReloadState>(
            listener: (context, state) {
              if (state.status == OrderingActiveReloadStatus.Loading) {
                timerSartCalButton();
                context.read<OrderingActiveReloadCubit>().onChangedStatus(OrderingActiveReloadStatus.Initial);
              }
            },
            child: SafeArea(
              bottom: widget.safeAreaButtom,
              // bottom: false,
              child: BlocBuilder<AuthBloc, AuthState>(
                builder: (context, stateAuth) {
                  return ActiveOrderBubbleOverlay(
                    contextMain: context,
                    bubbleIcon: SvgPicture.asset(
                      'assets/svgs/history-order.svg',
                    ),
                    isShowBubble: stateAuth.checkRole(PermissionBusiness.orders_orders_history.name),
                    onTap: () {
                      AppBased.go(
                        context,
                        AppRoutes.orderHistory,
                        args: ScreenArguments(data: true),
                      );
                    },
                    child: BlocConsumer<OrdersActiveCubit, OrdersActiveState>(
                      listener: (context, state) {
                        switch (state.status) {
                          case OrdersActiveStatus.Error:
                            if (state.errorMsg != null && state.errorMsg != '') {
                              AppBased.toastError(context, title: state.errorMsg);
                              BlocProvider.of<OrdersActiveCubit>(context).onResetStatus();
                              // if (_timerActiveOrders == null || _timerActiveOrders?.isActive == false) {
                              timerSartCalButton();
                              // }
                            }
                            return;
                          case OrdersActiveStatus.Success:
                            // if (_timerActiveOrders == null || _timerActiveOrders?.isActive == false) {
                            timerSartCalButton();
                            // }
                            return;
                          default:
                        }
                      },
                      builder: (context, state) {
                        switch (state.status) {
                          case OrdersActiveStatus.Loading:
                            return const OrdersActiveLoadingWidget();
                          case OrdersActiveStatus.Error:
                            return _buildEmpty(context);
                          case OrdersActiveStatus.Success:
                            final orders = state.allOrders ?? [];
                            final newOrders = state.newOrders ?? [];
                            final inprogresOrders = state.inprogresOrders ?? [];
                            final readyOrders = state.readyOrders ?? [];
                            final scheduledOrders = state.scheduledOrders ?? [];
                            return TabBarView(
                              controller: _tabController,
                              children: [
                                OrdersActiveListWidget(orders: orders, isHomeWidget: widget.isHomeWidget, isHorizontal: widget.isHorizontal),
                                OrdersActiveListWidget(orders: newOrders, isHomeWidget: widget.isHomeWidget, isHorizontal: widget.isHorizontal),
                                OrdersActiveListWidget(orders: inprogresOrders, isHomeWidget: widget.isHomeWidget, isHorizontal: widget.isHorizontal),
                                OrdersActiveListWidget(orders: readyOrders, isHomeWidget: widget.isHomeWidget, isHorizontal: widget.isHorizontal),
                                OrdersActiveListWidget(orders: scheduledOrders, isHomeWidget: widget.isHomeWidget, isHorizontal: widget.isHorizontal),
                              ],
                            );
                          default:
                            return const SizedBox.shrink();
                        }
                      },
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _changeTab(int? index) {
    setState(() {
      _tabController.animateTo(index ?? 1);
    });
    context.read<OrderActiveChangeTabCubit>().onChangedTab(OrderActiveChangeTabStatus.None);
  }

  Widget _buildEmpty(BuildContext context) {
    final l10n = context.l10n;
    return Center(
      child: RefreshIndicator(
        color: AppColors.appColor,
        onRefresh: () => context.read<OrdersActiveCubit>().getOrdersActive(loading: false),
        child: CustomScrollView(
          primary: false,
          physics: const AlwaysScrollableScrollPhysics(
            parent: ClampingScrollPhysics(),
          ),
          slivers: [
            SliverFillRemaining(
              hasScrollBody: false,
              fillOverscroll: true,
              child: Center(
                child: OrdersActiveEmptyWidget(
                  msg: l10n.noOrdersyet,
                  iconName: 'order-empty',
                  width: 120,
                  height: 120,
                  fontSize: 18,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({String title = '', String? iconName, Function? onTap}) {
    return ListTile(
      horizontalTitleGap: 16,
      minVerticalPadding: 0,
      contentPadding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      title: Text(
        title,
        style: const TextStyle(
          color: AppColors.lightPrimaryBackgroundColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      leading: SvgPicture.asset(
        'assets/svgs/$iconName.svg',
        colorFilter: const ColorFilter.mode(
          AppColors.lightPrimaryBackgroundColor,
          BlendMode.srcIn,
        ),
      ),
      onTap: () => onTap?.call(),
    );
  }
}

class OrderActiveTab extends StatelessWidget {
  final String label;
  final int count;

  const OrderActiveTab({required this.label, required this.count, super.key});

  @override
  Widget build(BuildContext context) {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (count > 0) ...{
            const SizedBox(width: 4),
            Container(
              constraints: const BoxConstraints(
                minWidth: 24,
                minHeight: 24,
              ),
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AppColors.tabsBadgeColor,
                borderRadius: BorderRadius.circular(99),
              ),
              child: Text(
                count.toString(),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.lightPrimaryBackgroundColor,
                ),
              ),
            ),
          },
        ],
      ),
    );
  }
}
