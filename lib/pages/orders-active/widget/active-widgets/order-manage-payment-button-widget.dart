// ignore_for_file: strict_raw_type, empty_catches

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/customer-order-payment-send-phone/customer-order-payment-send-phone.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customer-order-payment/customer-order-payment.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail-auto-reload/order-detail-auto-reload.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail/order-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@utils/currency-input-formatter/money-input-enums.dart';
import 'package:stickyqrbusiness/@utils/currency.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/currency/currency.model.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/active-widgets/order-customer-payment-send-link-widget.dart';

class OrderManagePaymentButtonWidget extends StatefulWidget {
  final Order? order;
  final double? height;
  final double? fontSize;
  final Color? labelColor;
  final Color? buttonBackgroundColor;
  final double? borderRadius;
  final bool isListOrder;
  const OrderManagePaymentButtonWidget({
    super.key,
    this.order,
    this.height,
    this.fontSize,
    this.labelColor = AppColors.appBlackColor,
    this.buttonBackgroundColor = AppColors.lightPrimaryBackgroundColor,
    this.borderRadius = 12,
    this.isListOrder = false,
  });

  @override
  State<OrderManagePaymentButtonWidget> createState() => _OrderManagePaymentButtonWidgetState();
}

class _OrderManagePaymentButtonWidgetState extends State<OrderManagePaymentButtonWidget> {
  final FocusNode _focusAmount = FocusNode();
  bool _isDisposed = false;
  final List<BuildContext> _dialogContexts = [];

  void _closeAllDialogs() {
    try {
      // Close all dialogs from top to bottom
      for (int i = _dialogContexts.length - 1; i >= 0; i--) {
        final dialogContext = _dialogContexts[i];
        if (Navigator.canPop(dialogContext)) {
          Navigator.of(dialogContext).pop();
        }
      }
      _dialogContexts.clear();
      context.read<OrderDetailAutoReloadCubit>().onChangedCloseDialog(closeDialog: OrderDetailAutoCloseDialogStatus.Close);
    } catch (e) {}
  }

  @override
  void dispose() {
    try {
      _isDisposed = true;
      _closeAllDialogs();
    } catch (e) {}

    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) {
        _isDisposed = true;
        _closeAllDialogs();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isFontMobile = MediaQuery.of(context).size.width < 600 || MediaQuery.of(context).size.height < 600;
    final bool isTablet = MediaQuery.of(context).size.width >= 600;
    final l10n = context.l10n;
    return BlocProvider(
      create: (context) => CustomerOrderPaymentCubit(),
      child: ButtonLoading(
        height: widget.height ?? (isFontMobile ? 48 : 56),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius ?? 12),
          side: const BorderSide(color: AppColors.iconColor, width: 1),
        ),
        borderRadius: widget.borderRadius,
        padding: EdgeInsets.symmetric(horizontal: isTablet ? 16 : 8),
        iconDirection: TextDirection.rtl,
        textDirection: TextDirection.rtl,
        label: l10n.managePayment,
        fontSize: widget.fontSize ?? (isFontMobile ? 18 : 20),
        circularLoadingSize: 24,
        fontWeight: FontWeight.w600,
        circularStrokeColor: AppColors.appColor,
        marginLoadingIcon: EdgeInsets.zero,
        labelColorDisable: AppColors.appBlackColor,
        labelColor: widget.labelColor,
        buttonBackgroundColor: widget.buttonBackgroundColor,
        callback: () {
          showDialogPayments(
            context,
            title: l10n.managePayment,
          );
        },
      ),
    );
  }

  void showDialogPayments(
    BuildContext context, {
    String? title,
    ValueChanged? onChanged,
  }) {
    if (_isDisposed) return;
    final l10n = context.l10n;
    final currencyCode = context.read<AuthBloc>().state.currencyBusiness;

    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext dialogContext) {
        if (!_dialogContexts.contains(dialogContext)) {
          _dialogContexts.add(dialogContext);
        }
        return BlocConsumer<CustomerOrderPaymentCubit, CustomerOrderPaymentState>(
          listener: (context, state) {
            if (state.status == CustomerOrderPaymentStatus.Error) {
              if (state.errorMsg != null && state.errorMsg != '') {
                AppBased.toastError(context, title: state.errorMsg);
                context.read<CustomerOrderPaymentCubit>().onResetStatus();
              }
            } else {
              if (state.status == CustomerOrderPaymentStatus.Success) {
                if (!widget.isListOrder) {
                  OrderDetailCubitRegistry.refreshOrder(widget.order?.id ?? '');
                }
                context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
                Future.delayed(const Duration(milliseconds: 1000), () {
                  _closeAllDialogs();
                });
              } else if (state.status == CustomerOrderPaymentStatus.SuccessShowQr) {
                context.read<CustomerOrderPaymentCubit>().onResetStatus();
                final String? url = state.order?.paymentLink;
                if (url != null) {
                  _closeAllDialogs();
                  showDialogPaymentShowQR(
                    context,
                    title: l10n.total,
                    url: url,
                    onChanged: (value) => {},
                  );
                }
              } else if (state.status == CustomerOrderPaymentStatus.SuccessSendPaymentLink) {
                context.read<CustomerOrderPaymentCubit>().onResetStatus();
                final String? url = state.order?.paymentLink;
                if (url != null) {
                  _closeAllDialogs();
                  showDialogPaymentShowPhone(
                    context,
                    title: l10n.confirmPhoneNumber,
                    phone: widget.order?.customer?.phone ?? widget.order?.phone,
                    url: url,
                    onChanged: (value) {},
                  );
                }
              }
            }
          },
          builder: (context, state) {
            return BlocListener<OrderDetailAutoReloadCubit, OrderDetailAutoReloadState>(
              listener: (context, state) {
                if (state.closeDialog == OrderDetailAutoCloseDialogStatus.Close) {
                  _closeAllDialogs();
                }
              },
              child: AlertDialog(
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(24)),
                ),
                titlePadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.zero,
                iconPadding: EdgeInsets.zero,
                buttonPadding: EdgeInsets.zero,
                insetPadding: const EdgeInsets.all(16),
                title: SizedBox(
                  height: 52,
                  child: Stack(
                    children: [
                      Positioned.fill(
                        top: 16,
                        child: Text(
                          title ?? '',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Positioned(
                        right: 8,
                        top: 0,
                        child: Padding(
                          padding: const EdgeInsets.all(2.0),
                          child: SizedBox(
                            width: 40,
                            child: MaterialButton(
                              elevation: 0,
                              highlightElevation: 0,
                              hoverElevation: 0,
                              hoverColor: AppColors.lightPrimaryBackgroundColor,
                              onPressed: () => {
                                Navigator.of(context).pop(),
                              },
                              color: AppColors.lightPrimaryBackgroundColor,
                              padding: EdgeInsets.zero,
                              shape: const CircleBorder(),
                              child: SvgPicture.asset(
                                'assets/svgs/close.svg',
                                colorFilter: const ColorFilter.mode(
                                  AppColors.appBlackColor,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                content: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: Container(
                    constraints: const BoxConstraints(
                      minWidth: 450,
                      maxWidth: 450,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.only(top: 16, bottom: 16),
                          margin: const EdgeInsets.only(left: 16, right: 16, top: 16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: AppColors.orderingIntegrationColor,
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Text(
                                  l10n.total,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Text(
                                CurrencyHelper.convertMoney((widget.order?.total ?? 0).toString(), code: widget.order?.currencyCode ?? currencyCode ?? 'USD'),
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  fontSize: 32,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(top: 16, left: 16, right: 16),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Text(
                                  l10n.cardPayments,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.appBlackColor,
                                  ),
                                ),
                              ),
                              _buildItemAlert(
                                l10n.sendPaymentLinkTitle,
                                isLoading: state.status == CustomerOrderPaymentStatus.LoadingSendPaymentLink,
                                onTap: () {
                                  context.read<CustomerOrderPaymentCubit>()
                                    ..onChangedType(CustomerOrderPaymentType.CARD)
                                    ..onCreateOrderPayment(
                                      status: CustomerOrderPaymentStatus.LoadingSendPaymentLink,
                                      id: widget.order?.id ?? '',
                                    ).then((value) {});
                                },
                              ),
                              const SizedBox(height: 16),
                              _buildItemAlert(
                                l10n.showQR,
                                isLoading: state.status == CustomerOrderPaymentStatus.LoadingShowQr,
                                onTap: () {
                                  context.read<CustomerOrderPaymentCubit>()
                                    ..onChangedType(CustomerOrderPaymentType.CARD)
                                    ..onCreateOrderPayment(
                                      status: CustomerOrderPaymentStatus.LoadingShowQr,
                                      id: widget.order?.id ?? '',
                                    ).then((value) {});
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildDivider(),
                        const SizedBox(height: 16),
                        Container(
                          margin: const EdgeInsets.only(left: 16, right: 16),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Text(
                                  l10n.cash,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.appBlackColor,
                                  ),
                                ),
                              ),
                              _buildItemAlert(
                                l10n.markAsPaidInCash,
                                isBorder: false,
                                isLoading: state.status == CustomerOrderPaymentStatus.Loading,
                                onTap: () {
                                  AppBased.showDialogYesNo(
                                    context,
                                    title: l10n.confirm,
                                    noText: l10n.no,
                                    yesText: l10n.yes,
                                    msgContent: l10n.collectCash,
                                    noTap: () {},
                                    yesTap: () {
                                      context.read<CustomerOrderPaymentCubit>()
                                        ..onChangedType(CustomerOrderPaymentType.CASH)
                                        ..onCreateOrderPayment(
                                          status: CustomerOrderPaymentStatus.Loading,
                                          id: widget.order?.id ?? '',
                                        ).then((value) {});
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  Widget _buildDivider() {
    return ClipRRect(
      child: Container(
        height: 8,
        width: double.infinity,
        color: AppColors.appBGAvatarColor,
      ),
    );
  }

  Widget _buildItemAlert(
    String title, {
    Function? onTap,
    bool isBorder = true,
    bool isLoading = false,
  }) {
    return ButtonLoading(
      height: 56,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.iconColor, width: 1),
      ),
      borderRadius: 12,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      // isAllowPress: isLoading,
      isLoading: isLoading,
      iconDirection: TextDirection.rtl,
      textDirection: TextDirection.rtl,
      label: title,
      fontSize: 20,
      circularLoadingSize: 24,
      fontWeight: FontWeight.w600,
      labelColor: AppColors.appBlackColor,
      circularStrokeColor: AppColors.appColor,
      marginLoadingIcon: EdgeInsets.zero,
      labelColorDisable: AppColors.appBlackColor,
      buttonBackgroundColor: AppColors.lightPrimaryBackgroundColor,
      callback: () {
        onTap?.call();
      },
    );
  }

  Widget _buildInputAmount(BuildContext context, String amount) {
    final l10n = context.l10n;

    final isCurrencyVND = context.read<AuthBloc>().state.currencyBusiness?.toLowerCase() == 'VND';
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 20),
            child: Text(
              l10n.total,
              style: const TextStyle(
                fontSize: 20,
              ),
            ),
          ),
          // const SizedBox(height: 20),
          Center(
            child: BlocBuilder<CustomerOrderPaymentCubit, CustomerOrderPaymentState>(
              builder: (context, state) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IntrinsicWidth(
                      child: TextFormField(
                        initialValue: amount,
                        readOnly: true,
                        cursorColor: AppColors.cursorColor,
                        cursorWidth: 1,
                        focusNode: _focusAmount,
                        autofocus: true,
                        keyboardType: isCurrencyVND ? TextInputType.number : const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          if (isCurrencyVND) CurrencyInputFormatter(thousandSeparator: ThousandSeparator.Period, mantissaLength: 0) else CurrencyInputFormatter(),
                        ],
                        onChanged: (String value) {
                          context.read<CustomerOrderPaymentCubit>().onChangeAmount(value);
                        },
                        textAlign: isCurrencyVND ? TextAlign.right : TextAlign.left,
                        style: const TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                          color: AppColors.appBlackColor,
                        ),
                        maxLength: 16,
                        decoration: InputDecoration(
                          counterText: '',
                          contentPadding: const EdgeInsets.only(top: 2),
                          border: InputBorder.none,
                          hintText: '0.00',
                          hintStyle: TextStyle(
                            fontSize: 36,
                            color: Colors.grey.shade400,
                          ),
                          prefixIconConstraints: const BoxConstraints(
                            minWidth: 0,
                            minHeight: 0,
                          ),
                          suffixIconConstraints: const BoxConstraints(
                            minWidth: 0,
                            minHeight: 0,
                          ),
                          prefixIcon: !isCurrencyVND
                              ? Padding(
                                  padding: const EdgeInsets.only(right: 2),
                                  child: Text(
                                    getCurrencySymbol(context) ?? '',
                                    textAlign: TextAlign.end,
                                    softWrap: true,
                                    maxLines: 1,
                                    style: const TextStyle(
                                      fontSize: 34,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.appBlackColor,
                                    ),
                                  ),
                                )
                              : null,
                          suffixIcon: isCurrencyVND
                              ? Transform.translate(
                                  offset: const Offset(0, -2),
                                  child: Text(
                                    getCurrencySymbol(context) ?? '',
                                    textAlign: TextAlign.end,
                                    softWrap: true,
                                    maxLines: 1,
                                    style: const TextStyle(
                                      fontSize: 34,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.appBlackColor,
                                    ),
                                  ),
                                )
                              : null,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  String? getCurrencySymbol(BuildContext context) {
    final currency = currencyList.firstWhere(
      (currency) => currency.code?.toLowerCase() == context.read<AuthBloc>().state.currencyBusiness?.toLowerCase(),
    );
    return currency.symbol;
  }

  void showDialogPaymentShowQR(
    BuildContext context, {
    String? title,
    required String? url,
    ValueChanged? onChanged,
  }) {
    if (_isDisposed) return;
    final l10n = context.l10n;
    final currencyCode = context.read<AuthBloc>().state.currencyBusiness;

    showDialog<String>(
      barrierDismissible: false,
      context: context,
      useSafeArea: false,
      builder: (BuildContext dialogContext) {
        if (!_dialogContexts.contains(dialogContext)) {
          _dialogContexts.add(dialogContext);
        }
        return OrientationBuilder(
          builder: (context, orientation) {
            final bool isTablet = MediaQuery.of(context).size.width >= 600;

            return BlocListener<OrderDetailAutoReloadCubit, OrderDetailAutoReloadState>(
              listener: (context, state) {
                if (state.closeDialog == OrderDetailAutoCloseDialogStatus.Close) {
                  _closeAllDialogs();
                }
              },
              child: Dialog(
                backgroundColor: AppColors.lightPrimaryBackgroundColor,
                insetPadding: EdgeInsets.zero,
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: AppColors.lightPrimaryBackgroundColor,
                  child: SafeArea(
                    bottom: false,
                    child: Stack(
                      children: [
                        Positioned.fill(
                          child: Center(
                            child: SingleChildScrollView(
                              physics: const ClampingScrollPhysics(),
                              padding: EdgeInsets.only(top: isTablet ? 56 : 16, bottom: 32),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Column(
                                    children: [
                                      Text(
                                        title ?? '',
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.appBlackColor,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        CurrencyHelper.convertMoney((widget.order?.total ?? 0).toString(), code: widget.order?.currencyCode ?? currencyCode ?? 'USD'),
                                        style: const TextStyle(
                                          fontSize: 32,
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.appBlackColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  QrImageView(
                                    data: url ?? '',
                                    version: QrVersions.auto,
                                    dataModuleStyle: const QrDataModuleStyle(
                                      color: AppColors.appBlackColor,
                                      dataModuleShape: QrDataModuleShape.circle,
                                    ),
                                    padding: EdgeInsets.zero,
                                    size: isTablet ? 300 : 248,
                                    gapless: false,
                                    backgroundColor: AppColors.lightPrimaryBackgroundColor,
                                  ),
                                  Container(
                                    padding: const EdgeInsets.only(top: 16),
                                    child: Text(
                                      l10n.scanQrCodeToPay,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.appBlackColor,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                        // Header với close button (phía trên, overlay)
                        Positioned(
                          top: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            color: AppColors.appTransparentColor,
                            height: 56,
                            child: Stack(
                              children: [
                                Positioned(
                                  right: 8,
                                  top: 0,
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: MaterialButton(
                                      elevation: 0,
                                      highlightElevation: 0,
                                      hoverElevation: 0,
                                      hoverColor: AppColors.lightPrimaryBackgroundColor,
                                      onPressed: () => Navigator.of(context).pop(),
                                      color: AppColors.lightPrimaryBackgroundColor,
                                      padding: EdgeInsets.zero,
                                      shape: const CircleBorder(),
                                      child: SvgPicture.asset(
                                        'assets/svgs/close.svg',
                                        colorFilter: const ColorFilter.mode(
                                          AppColors.appBlackColor,
                                          BlendMode.srcIn,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  void showDialogPaymentShowPhone(
    BuildContext context, {
    String? title,
    required String? url,
    required String? phone,
    ValueChanged? onChanged,
  }) {
    if (_isDisposed) return;
    final l10n = context.l10n;
    final currencyCode = context.read<AuthBloc>().state.currencyBusiness;
    context.read<CustomerOrderPaymentSendPhoneCubit>()
      ..onChangedPaymentLink(url ?? '')
      ..onChangedPhoneNumber(phone ?? '');
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext dialogContext) {
        if (!_dialogContexts.contains(dialogContext)) {
          _dialogContexts.add(dialogContext);
        }
        return OrientationBuilder(
          builder: (context, orientation) {
            return BlocListener<OrderDetailAutoReloadCubit, OrderDetailAutoReloadState>(
              listener: (context, state) {
                if (state.closeDialog == OrderDetailAutoCloseDialogStatus.Close) {
                  _closeAllDialogs();
                }
              },
              child: AlertDialog(
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12.0)),
                ),
                titlePadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.zero,
                iconPadding: EdgeInsets.zero,
                buttonPadding: EdgeInsets.zero,
                insetPadding: const EdgeInsets.all(16),
                title: SizedBox(
                  height: 52,
                  child: Stack(
                    children: [
                      Positioned.fill(
                        top: 16,
                        child: Text(
                          title ?? '',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Positioned(
                        right: 8,
                        top: 0,
                        child: Padding(
                          padding: const EdgeInsets.all(2.0),
                          child: SizedBox(
                            width: 40,
                            child: MaterialButton(
                              elevation: 0,
                              highlightElevation: 0,
                              hoverElevation: 0,
                              hoverColor: AppColors.lightPrimaryBackgroundColor,
                              onPressed: () => {
                                Navigator.of(context).pop(),
                              },
                              color: AppColors.lightPrimaryBackgroundColor,
                              padding: EdgeInsets.zero,
                              shape: const CircleBorder(),
                              child: SvgPicture.asset(
                                'assets/svgs/close.svg',
                                colorFilter: const ColorFilter.mode(
                                  AppColors.appBlackColor,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                content: BlocConsumer<CustomerOrderPaymentSendPhoneCubit, CustomerOrderPaymentSendPhoneState>(
                  listener: (context, state) {
                    switch (state.status) {
                      case CustomerOrderPaymentSendPhoneStatus.Error:
                        if (state.errorMsg != null && state.errorMsg != '') {
                          AppBased.toastError(context, title: state.errorMsg);
                          BlocProvider.of<CustomerOrderPaymentSendPhoneCubit>(context).onResetStatus();
                        }
                        return;
                      case CustomerOrderPaymentSendPhoneStatus.Success:
                        AppBased.toastSuccess(context, title: l10n.successfully);
                        BlocProvider.of<CustomerOrderPaymentSendPhoneCubit>(context).onResetStatus();

                        Navigator.pop(context);

                        return;
                      default:
                    }
                  },
                  builder: (context, statePayment) {
                    return Container(
                      constraints: const BoxConstraints(
                        minWidth: 450,
                        maxWidth: 450,
                      ),
                      child: SingleChildScrollView(
                        primary: true,
                        physics: const AlwaysScrollableScrollPhysics(
                          parent: ClampingScrollPhysics(),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 24.0, bottom: 24),
                              child: OrderCustomerPaymentSendLinkWidget(
                                user: User(phone: phone),
                                currencyBusiness: currencyCode ?? 'USD',
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 24),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: ButtonLoading(
                                      height: 56,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        side: const BorderSide(color: AppColors.iconColor, width: 1),
                                      ),
                                      borderRadius: 12,
                                      padding: const EdgeInsets.symmetric(horizontal: 8),
                                      iconDirection: TextDirection.rtl,
                                      textDirection: TextDirection.rtl,
                                      label: l10n.cancel,
                                      fontSize: 16,
                                      circularLoadingSize: 24,
                                      fontWeight: FontWeight.w600,
                                      labelColor: AppColors.appBlackColor,
                                      circularStrokeColor: AppColors.appColor,
                                      marginLoadingIcon: EdgeInsets.zero,
                                      labelColorDisable: AppColors.appBlackColor,
                                      buttonBackgroundColor: AppColors.lightPrimaryBackgroundColor,
                                      callback: () {
                                        Navigator.pop(context);
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: ButtonLoading(
                                      height: 56,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        side: const BorderSide(color: AppColors.iconColor, width: 1),
                                      ),
                                      borderRadius: 12,
                                      padding: const EdgeInsets.symmetric(horizontal: 8),
                                      isAllowPress: statePayment.status != CustomerOrderPaymentSendPhoneStatus.Loading,
                                      isLoading: statePayment.status == CustomerOrderPaymentSendPhoneStatus.Loading,
                                      iconDirection: TextDirection.rtl,
                                      textDirection: TextDirection.rtl,
                                      label: l10n.send,
                                      fontSize: 16,
                                      circularLoadingSize: 24,
                                      fontWeight: FontWeight.w600,
                                      labelColor: AppColors.lightPrimaryBackgroundColor,
                                      circularStrokeColor: AppColors.appColor,
                                      marginLoadingIcon: EdgeInsets.zero,
                                      labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                                      buttonBackgroundColor: AppColors.appBlackColor,
                                      callback: () {
                                        final String phone = statePayment.phone.replaceAll(RegExp(r'\D'), '');

                                        context.read<CustomerOrderPaymentSendPhoneCubit>().doSendPaymentLink(
                                              id: widget.order?.id ?? '',
                                              phone: phone,
                                              url: url ?? '',
                                            );
                                      },
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            );
          },
        );
      },
    ).then((value) => onChanged?.call(value));
  }
}
