import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/app-based.dart';
import 'package:stickyqrbusiness/@core/models/screen-args.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile.cubit.dart';
import 'package:stickyqrbusiness/@core/store/setting-accept-online-ordering/setting-accept-online-ordering.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/@orders-active-widget.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';

class OrdersActiveDrawerWidget extends StatelessWidget {
  final OrderDrawerPageActive activePage;
  final bool isFromHome;
  const OrdersActiveDrawerWidget({super.key, required this.activePage, this.isFromHome = false});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Drawer(
      width: 350,
      backgroundColor: AppColors.drawerBGColor,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: ListView(
            physics: const ClampingScrollPhysics(),
            children: [
              // const DrawerHeader(
              //   decoration: BoxDecoration(color: Colors.blue),
              //   child: Text('Menu', style: TextStyle(color: AppColors.lightPrimaryBackgroundColor, fontSize: 24)),
              // ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    splashRadius: 16,
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.lightPrimaryBackgroundColor.withValues(alpha: .2),
                      shape: const CircleBorder(),
                    ),
                    icon: SvgPicture.asset(
                      'assets/svgs/close.svg',
                      colorFilter: const ColorFilter.mode(
                        AppColors.lightPrimaryBackgroundColor,
                        BlendMode.srcIn,
                      ),
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  final nameBusiness = state.nameBusiness ?? 'N/A';
                  final logoBusiness = state.logoBusinessID != null ? '${AppBased.appEnv.cdnUrl}${state.logoBusinessID}' : state.logoBusiness;
                  final address = state.business?.address?.street ?? '';
                  return AccountProfileItemWidget(
                    isBorder: false,
                    width: 48,
                    height: 48,
                    icon: '',
                    linkAvatar: logoBusiness,
                    titleValue: nameBusiness,
                    fontWeight: FontWeight.w600,
                    fontWeightSubTitle: FontWeight.w500,
                    fontSize: 16,
                    fontSizeSubTitle: 12,
                    subTitle: address,
                    textColor: AppColors.lightPrimaryBackgroundColor,
                    textColorSubTitle: AppColors.lightPrimaryBackgroundColor,
                  );
                },
              ),
              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, stateAuth) {
                  // final isAcceptOnlineOrdering = state.business?.orderAllowOnlineOrdering ?? false;
                  final business = stateAuth.business;
                  final timeZone = context.read<AuthBloc>().state.businessTimeZone ?? '';
                  final isAcceptOnlineOrdering = business?.orderAllowOnlineOrdering ?? false;
                  final storeStt = business?.orderOperationStatus;
                  final storeOperationSttUntil = business?.orderOperationStatusUntil;
                  final statusOnline = getStoreStatus(
                    context: context,
                    l10n: l10n,
                    timezone: timeZone,
                    acceptOnlineOrdering: isAcceptOnlineOrdering,
                    stt: storeStt ?? '',
                    operationStatusUntil: storeOperationSttUntil,
                  );
                  // print('statusOnline == $statusOnline');
                  if (!stateAuth.checkRole(PermissionBusiness.orders_orders_settings.name)) {
                    return const SizedBox.shrink();
                  }

                  return BlocProvider(
                    create: (context) => SettingOrderAcceptOnlineOrderingCubit()..onChangeToggle(isAcceptOnlineOrdering),
                    child: BlocConsumer<SettingOrderAcceptOnlineOrderingCubit, SettingOrderAcceptOnlineOrderingState>(
                      listener: (context, state) {
                        final stt = state.status;
                        AppBased.loading(isShow: stt == AcceptOnlineOrderingStatus.Loading);
                        switch (stt) {
                          case AcceptOnlineOrderingStatus.Success:
                            context.read<BusinessProfileCubit>().getBusinessProfile();
                            break;
                          case AcceptOnlineOrderingStatus.Error:
                            if (state.errorMsg?.isNotEmpty == true) {
                              AppBased.toastError(context, title: state.errorMsg);
                            }
                            break;
                          default:
                            break;
                        }
                      },
                      builder: (context, state) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: InkWell(
                            onTap: () {
                              AppBased.go(context, AppRoutes.updateStoreStatus);
                            },
                            borderRadius: BorderRadius.circular(99),
                            child: Container(
                              // margin: const EdgeInsets.only(top: 8),
                              padding: const EdgeInsets.fromLTRB(12, 8, 8, 8),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(99),
                                border: Border.all(
                                  width: 1,
                                  color: Colors.white12,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: !isAcceptOnlineOrdering ? const Color(0xFF595D62) : getStoreSttColor(storeStt ?? ''),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Text(
                                      statusOnline,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),

              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Column(
                      children: [
                        if (!isFromHome && state.checkPermissions(PermissionBusiness.orders_active_orders.name, [1, 2, 3, 4])) _buildMenuItem(title: l10n.activeOrders, iconName: 'cart-order', isActive: activePage == OrderDrawerPageActive.ActiveOrders, onTap: () => _onDrawerItemSelected(context, OrderDrawerPageActive.ActiveOrders)),
                        if (state.checkPermissions(PermissionBusiness.orders_orders_history.name, [1, 2, 3, 4]))
                          _buildMenuItem(
                            title: l10n.ordersHistory,
                            iconName: 'order-menu-history',
                            isActive: activePage == OrderDrawerPageActive.OrdersHistory,
                            onTap: () => !isFromHome
                                ? _onDrawerItemSelected(context, OrderDrawerPageActive.OrdersHistory)
                                : AppBased.go(
                                    context,
                                    AppRoutes.orderHistory,
                                    args: ScreenArguments(data: true),
                                  ),
                          ),
                        if (state.checkPermissions(PermissionBusiness.orders_products.name, [1, 2, 3, 4]) || state.checkPermissions(PermissionBusiness.orders_products_categories.name, [1, 2, 3, 4]) || state.checkPermissions(PermissionBusiness.orders_products_modifiers.name, [1, 2, 3, 4]))
                          _buildMenuItem(
                            title: l10n.productsManage,
                            iconName: 'order-menu-product',
                            isActive: activePage == OrderDrawerPageActive.ManagementProducts,
                            // onTap: () => _onDrawerItemSelected(context, OrderDrawerPageActive.ManagementProducts),
                            onTap: () => !isFromHome
                                ? _onDrawerItemSelected(context, OrderDrawerPageActive.ManagementProducts)
                                : AppBased.go(
                                    context,
                                    AppRoutes.managementProduct,
                                    args: ScreenArguments(data: true),
                                  ),
                          ),
                        if (state.checkPermissions(PermissionBusiness.orders_orders_settings.name, [1, 2, 3, 4]))
                          _buildMenuItem(
                            title: l10n.settings,
                            iconName: 'order-menu-settings',
                            isActive: activePage == OrderDrawerPageActive.Settings,
                            isShowTrailing: true,
                            // onTap: () => _onDrawerItemSelected(context, OrderDrawerPageActive.Settings),
                            onTap: () => !isFromHome
                                ? _onDrawerItemSelected(context, OrderDrawerPageActive.Settings)
                                : AppBased.go(
                                    context,
                                    AppRoutes.settingsOrders,
                                    args: ScreenArguments(data: true),
                                  ),
                          ),
                        const SizedBox(height: 12),
                        // if (activePage == OrderDrawerPageActive.ActiveOrders)
                        if (!isFromHome)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: _buildMenuItem(
                              title: l10n.exitOrdering,
                              iconName: 'order-menu-exit',
                              onTap: () {
                                _onDrawerItemSelected(context, OrderDrawerPageActive.Exit);

                                SystemChrome.setPreferredOrientations([
                                  DeviceOrientation.portraitUp,
                                  DeviceOrientation.portraitDown,
                                  DeviceOrientation.landscapeLeft,
                                  DeviceOrientation.landscapeRight,
                                ]);
                              },
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    String title = '',
    String? iconName,
    Function? onTap,
    bool isActive = false,
    bool isShowTrailing = false,
  }) {
    return Card(
      color: isActive ? AppColors.lightPrimaryBackgroundColor.withValues(alpha: .1) : Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      elevation: 0,
      margin: EdgeInsets.zero,
      clipBehavior: Clip.antiAlias, // splash color không bị overflow
      child: InkWell(
        onTap: () => onTap?.call(),
        child: ListTile(
          horizontalTitleGap: 16,
          minVerticalPadding: 0,
          contentPadding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
          title: Text(
            title,
            style: const TextStyle(
              color: AppColors.lightPrimaryBackgroundColor,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          leading: SvgPicture.asset(
            'assets/svgs/$iconName.svg',
            colorFilter: const ColorFilter.mode(
              AppColors.lightPrimaryBackgroundColor,
              BlendMode.srcIn,
            ),
          ),
          trailing: isShowTrailing
              ? const Icon(
                  Icons.arrow_forward_ios_outlined,
                  color: Colors.white,
                  size: 18,
                )
              : null,
        ),
      ),
    );
  }

  Color getStoreSttColor(String stt) {
    switch (stt.toUpperCase()) {
      case 'ACTIVE':
        return Colors.green;
      case 'BUSY':
        return Colors.orange;
      case 'PAUSED':
        return Colors.red;
      default:
        return Colors.green;
    }
  }

  String getStoreStatus({
    required BuildContext context,
    required AppLocalizations l10n,
    required String timezone,
    required bool acceptOnlineOrdering,
    required String stt,
    DateTime? operationStatusUntil,
  }) {
    final time = DateTimeHelper.timeFormat(context, operationStatusUntil, timeZone: timezone);

    if (!acceptOnlineOrdering) {
      return l10n.closed;
    }

    switch (stt.toUpperCase()) {
      case 'ACTIVE':
        if (operationStatusUntil != null) {
          return '${l10n.acceptingOrdersUntil} $time';
        }
        return l10n.acceptOnlineOrdering;
      case 'BUSY':
        return '${l10n.acceptingOrdersUntil} $time';
      case 'PAUSED':
        return '${l10n.pauseAcceptingOrdersUntil} $time';

      default:
        return l10n.acceptOnlineOrdering;
    }
  }

  void _onDrawerItemSelected(BuildContext context, OrderDrawerPageActive page) {
    final String? previousRouteName = AppValidations.routeStackObserver?.previousRoute?.settings.name;
    Navigator.pop(context);
    if (activePage != page) {
      switch (page) {
        case OrderDrawerPageActive.ActiveOrders:
          if (previousRouteName == 'ordering/active') {
            Navigator.pop(context);
          } else {
            AppBased.go(context, AppRoutes.orderActivePage, isReplace: true);
          }
          break;
        case OrderDrawerPageActive.OrdersHistory:
          AppBased.go(context, AppRoutes.orderHistory, isReplace: true);
          break;
        case OrderDrawerPageActive.ManagementProducts:
          AppBased.go(context, AppRoutes.managementProduct, isReplace: true, args: ScreenArguments(data: false));
          break;
        case OrderDrawerPageActive.Settings:
          if (previousRouteName == '/settings-orders') {
            Navigator.pop(context);
          } else {
            AppBased.go(context, AppRoutes.settingsOrders, isReplace: true);
          }
          break;
        default:
      }
    }
    // AppLog.e('Item selected: $page');
    if (page == OrderDrawerPageActive.Exit) Navigator.pop(context);
  }
}
