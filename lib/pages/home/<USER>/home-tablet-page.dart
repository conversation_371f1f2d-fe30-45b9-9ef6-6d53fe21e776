// ignore_for_file: use_decorated_box, avoid_dynamic_calls, avoid_print, must_be_immutable, use_build_context_synchronously

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-default/home-default.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-theme/home-theme.cubit.dart';
import 'package:stickyqrbusiness/@core/store/point-claims/point-claims.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-statistics/qr_statistics.cubit.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/home-engagement/home-engagement-widget.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/point-claims/point-claims.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/widgets/@container-widget.dart';
import 'package:stickyqrbusiness/pages/home-call-button-v2/home-call-button-v2-page.dart';
// import 'package:stickyqrbusiness/pages/home-call-button/home-call-button-page.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/@home-widgets.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/multiple-home-reorder/@home-reorder.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';

class HomeTabletPage extends StatelessWidget {
  GlobalKey menuButtonKey;
  HomeTabletPage({super.key, required this.menuButtonKey});

  @override
  Widget build(BuildContext context) {
    // final l10n = context.l10n;
    return BlocBuilder<HomeDefaultWidgetCubit, HomeDefaultWidgetState>(
      builder: (context, stateWidget) {
        final themeName = stateWidget.homeWidget?.settings?.appearance?.theme ?? 'theme-7';
        final themeColor = multipleColor.firstWhereOrNull((color) => color.nameColor == themeName);

        final homeWidget = themeColor?.colors ?? [];
        final checkAnnotatedRegion = themeColor?.busNameColor?.toUpperCase() == 'FFFFFF';
        final colorAppBar = Color(int.parse('0xFF${themeColor?.busNameColor ?? 'FFFFFF'}'));
        final colorStatusBar = checkAnnotatedRegion ? Colors.black : Colors.white;

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomLeft,
              colors: homeWidget.map((color) {
                return Color(int.parse('0xFF$color'));
              }).toList(),
            ),
          ),
          child: AnnotatedRegion<SystemUiOverlayStyle>(
            value: checkAnnotatedRegion ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark,
            child: Scaffold(
              backgroundColor: AppColors.appTransparentColor,
              appBar: _appBar(context, colorAppBar, colorStatusBar),
              body: RefreshIndicator(
                color: AppColors.appColor,
                onRefresh: () => _onRefresh(context),
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(
                    parent: ClampingScrollPhysics(),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 24.0),
                    child: BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, stateAuth) {
                        return BlocBuilder<HomeThemeCubit, HomeThemesState>(
                          builder: (context, state) {
                            // final theme = DefineHomeThemeConfig.getHomeTheme(state.themeName);
                            return Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const HomeUpgradeChangePlanPage(),
                                SafeArea(
                                  bottom: false,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                    child: BlocBuilder<HomeDefaultWidgetCubit, HomeDefaultWidgetState>(
                                      builder: (context, state) {
                                        if (state.homeWidget == null) {
                                          return Column(
                                            children: [
                                              const Padding(
                                                padding: EdgeInsets.only(top: 16.0),
                                                child: EngagementWidget(),
                                              ),
                                              if (stateAuth.checkRole(PermissionBusiness.give_points.name) || stateAuth.checkRole(PermissionBusiness.deduct_points.name) || stateAuth.checkRole(PermissionBusiness.redeem_rewards.name) || stateAuth.checkRole(PermissionBusiness.redeem_offer.name)) ...{
                                                const Padding(
                                                  padding: EdgeInsets.only(top: 16),
                                                  child: FindOrScanWidget(),
                                                ),
                                              },
                                              if (stateAuth.checkRole(PermissionBusiness.give_points.name)) ...{
                                                const Padding(
                                                  padding: EdgeInsets.only(top: 16.0),
                                                  child: AddStickyWidget(),
                                                ),
                                              },
                                              // if (stateAuth.checkRole(PermissionBusiness.point_claims.name)) ...{
                                              //   const Padding(
                                              //     padding: EdgeInsets.only(top: 16.0),
                                              //     child: PointClaimsWidget(),
                                              //   ),
                                              // },
                                              // if (stateAuth.checkRole(PermissionBusiness.cb_manage_customers_requests.name)) ...{
                                              //   const Padding(
                                              //     padding: EdgeInsets.only(top: 16.0),
                                              //     child: HomeCallButtonPage(),
                                              //   ),
                                              // },
                                              // HomeCustomizeWidget(titleColor: colorAppBar),
                                            ],
                                          );
                                        }

                                        return Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: state.homeWidget!.settings!.widgets!.map((e) {
                                            return _buildListWidget(context, (e.id ?? '').toUpperCase());
                                          }).toList(),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildListWidget(BuildContext context, String type) {
    if (type.contains(HomeReorderDefine.STICKY.name)) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
        child: AddStickyWidget(),
      );
    } else if (type.contains(HomeReorderDefine.ENGAGEMENT.name)) {
      return const Padding(padding: EdgeInsets.only(top: 16.0), child: EngagementWidget());
    } else if (type.contains(HomeReorderDefine.POINT_CLAIMS.name)) {
      return const Padding(padding: EdgeInsets.only(top: 16.0), child: PointClaimsWidget());
    } else if (type.contains(HomeReorderDefine.CALL_BUTTON_SESSION.name)) {
      // return const Padding(padding: EdgeInsets.only(top: 16.0), child: HomeCallButtonPage());
      // return const Padding(padding: EdgeInsets.only(top: 16.0), child: HomeCallButtonPageVer2());
      return const Padding(padding: EdgeInsets.only(top: 16.0), child: SizedBox.shrink());
      // return Padding(padding: EdgeInsets.only(top: 16.0), child: Container(color: Colors.red, child: Text('call button tablet home'),));
    } else if (type.contains(HomeReorderDefine.CUSTOMERS.name)) {
      return const Padding(padding: EdgeInsets.only(top: 16.0), child: FindOrScanWidget());
    } else if (type.contains(HomeReorderDefine.VOUCHERS_CALENDAR.name)) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  AppBar _appBar(BuildContext context, Color colorAppBar, Color colorStatusBar) {
    return AppBar(
      backgroundColor: colorStatusBar.withValues(alpha: 0.001),
      elevation: 0,
      leadingWidth: 0,
      title: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, stateAuth) {
          final nameBusiness = stateAuth.nameBusiness ?? '';
          return BusinessProfileItemWidget(
            isBorder: false,
            onTap: () => null,
            // icon: 'assets/svgs/edit.svg',
            linkAvatar: stateAuth.logoBusiness,
            titleValue: nameBusiness,
            fontWeight: FontWeight.w700,
            fontSize: 16,
            textColor: colorAppBar,
          );
        },
      ),
      actions: [
        Padding(
          key: menuButtonKey,
          padding: const EdgeInsets.all(10.0),
          child: SizedBox(
            width: 40,
            child: MaterialButton(
              elevation: 0,
              highlightElevation: 0,
              hoverElevation: 0,
              hoverColor: AppColors.appTransparentColor,
              onPressed: () => AppBased.go(context, AppRoutes.settings),
              color: AppColors.appTransparentColor,
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
              child: SvgPicture.asset(
                'assets/svgs/menu.svg',
                colorFilter: ColorFilter.mode(
                  colorAppBar,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _onRefresh(BuildContext context) async {
    await context.read<QrStatisticsCubit>().onBusinessStatistics();
    // }
    await context.read<PointClaimsCubit>().getPointClaims(isLoading: false);
    await context.read<CallButtonCubit>().getSections(withLoading: false);
  }
}
