// ignore_for_file: must_be_immutable, empty_catches, use_decorated_box, prefer_is_empty, unawaited_futures

import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-default/home-default.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@core/store/point-claims/point-claims.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-statistics/qr_statistics.cubit.dart';
import 'package:stickyqrbusiness/@core/store/vouchers-calendar/voucher-calendar.cubit.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/home-engagement/home-engagement-widget.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/home-vouchers-calendar-widget.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/order-active-home-multi-widget.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/point-claims/point-claims.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/widgets/@container-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home-call-button-v2/home-call-button-v2-page.dart';
// import 'package:stickyqrbusiness/pages/home-call-button/home-call-button-page.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/multiple-home-reorder/@home-reorder.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/multiple-home-reorder/main-home-screen-widget.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';

class HomeMobileMultiPage extends StatefulWidget {
  GlobalKey menuButtonKey;
  HomeMobileMultiPage({super.key, required this.menuButtonKey});

  @override
  State<HomeMobileMultiPage> createState() => _HomeMobileMultiPageState();
}

class _HomeMobileMultiPageState extends State<HomeMobileMultiPage> with WidgetsBindingObserver {
  final PageController _pageController = PageController(
    initialPage: 0,
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HomeDefaultWidgetCubit, HomeDefaultWidgetState>(
      listener: (context, stateWidget) {},
      builder: (context, stateWidget) {
        List<MultipleHome> homesWidget = stateWidget.homesWidget ?? [];
        try {
          if (homesWidget.isEmpty) {
            homesWidget = [
              MultipleHome(
                isExistDB: false,
                settings: MultipleHomeSettings(
                  widgets: [
                    MultipleHomeWidget(
                      id: 'engagement',
                      index: 0,
                    ),
                    MultipleHomeWidget(
                      id: 'customers',
                      index: 1,
                    ),
                    MultipleHomeWidget(
                      id: 'sticky',
                      index: 2,
                    )
                  ],
                  appearance: Appearance(theme: 'theme-7'),
                ),
              )
            ];
          }

          final index = homesWidget.indexOf(homesWidget.firstWhere((obj) => obj.id == stateWidget.idSelected));

          _pageController.animateToPage(
            index < 0 ? 0 : index,
            duration: const Duration(milliseconds: 100),
            curve: Curves.fastOutSlowIn,
          );
        } catch (e) {}

        final homeLocalDefault = stateWidget.homeWidget ?? homesWidget[0];
        final themeName = homeLocalDefault.settings?.appearance?.theme ?? 'theme-7';
        final themeColor = multipleColor.firstWhereOrNull((color) => color.nameColor == themeName);
        final homeWidgetColor = themeColor?.colors ?? [];
        final checkAnnotatedRegion = themeColor?.busNameColor?.toUpperCase() == 'FFFFFF';
        final colorAppBar = Color(int.parse('0xFF${themeColor?.busNameColor ?? 'FFFFFF'}'));
        final colorStatusBar = checkAnnotatedRegion ? Colors.black : Colors.white;

        return GestureDetector(
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomLeft,
                colors: homeWidgetColor.map((color) {
                  return Color(int.parse('0xFF$color'));
                }).toList(),
              ),
            ),
            child: AnnotatedRegion<SystemUiOverlayStyle>(
              value: checkAnnotatedRegion ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark,
              child: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  return Scaffold(
                    backgroundColor: AppColors.appTransparentColor,
                    extendBody: true,
                    appBar: _appBar(context, colorAppBar, colorStatusBar),
                    bottomNavigationBar: View.of(context).viewInsets.bottom > 0
                        ? null
                        : Container(
                            padding: const EdgeInsets.fromLTRB(16, 0, 8, 0),
                            color: AppColors.appTransparentColor,
                            height: Platform.isAndroid ? 52 : 60,
                            child: const MainHomeScreenWidget(),
                          ),
                    body: SafeArea(
                      bottom: false,
                      child: PageView.builder(
                        physics: const ClampingScrollPhysics(),
                        controller: _pageController,
                        onPageChanged: (value) async {
                          final home = homesWidget[value];
                          context.read<HomeDefaultWidgetCubit>().onChangeSelectedHomeWidget(home, id: home.id);
                        },
                        itemCount: homesWidget.length,
                        itemBuilder: (BuildContext context, int index) {
                          return _buildHome(
                            context,
                            stateWidget,
                            index,
                            homesWidget[index],
                            themeColor,
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHome(
    BuildContext context,
    HomeDefaultWidgetState stateWidget,
    int indexHome,
    MultipleHome? home,
    MultipleColor? themeColor,
  ) {
    final l10n = context.l10n;
    final isOwner = context.read<AuthBloc>().state.businessOwner;
    final isEmptyWidget = home?.settings!.widgets?.length == 0 || home!.settings!.widgets!.isEmpty;

    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: RefreshIndicator(
          key: ValueKey(home),
          color: AppColors.appColor,
          onRefresh: () => _onRefresh(context),
          child: CustomScrollView(
            primary: true,
            shrinkWrap: true,
            physics: const AlwaysScrollableScrollPhysics(
              parent: ClampingScrollPhysics(),
            ),
            slivers: [
              if (isEmptyWidget) ...{
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(24, 0, 24, 0),
                    alignment: Alignment.center,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          MaterialButton(
                            elevation: 0,
                            highlightElevation: 0,
                            hoverElevation: 0,
                            hoverColor: AppColors.appTransparentColor,
                            onPressed: () {},
                            color: AppColors.appTransparentColor,
                            padding: EdgeInsets.zero,
                            shape: const CircleBorder(),
                            child: SvgPicture.asset(
                              'assets/svgs/home-widget-empty.svg',
                              // colorFilter: ColorFilter.mode(
                              //   Color(int.parse('0xFF${themeColor.busNameColor}')),
                              //   BlendMode.srcIn,
                              // ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.all(24),
                            child: Center(
                              child: Text(
                                isOwner ? l10n.homeEmptyOwner : l10n.homeEmptyStaff,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Color(int.parse('0xFF${themeColor?.busNameColor ?? 'FFFFFF'}')),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              } else
                SliverFillRemaining(
                  child: BlocBuilder<AuthBloc, AuthState>(
                    builder: (context, stateAuth) {
                      return ListView(
                        physics: const ClampingScrollPhysics(),
                        padding: const EdgeInsets.only(bottom: 64, left: 16, right: 16),
                        children: _buildWidget(context, home.settings!.widgets, stateAuth, themeColor),
                      );
                    },
                  ),
                ),
            ],
          )),
    );
  }

  List<Widget> _buildWidget(BuildContext context, List<MultipleHomeWidget>? widgetsData, AuthState stateAuth, MultipleColor? themeColor) {
    final l10n = context.l10n;
    final isOwner = context.read<AuthBloc>().state.businessOwner;
    if (widgetsData?.length == 0 || widgetsData!.isEmpty) {
      return [
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              MaterialButton(
                elevation: 0,
                highlightElevation: 0,
                hoverElevation: 0,
                hoverColor: AppColors.appTransparentColor,
                onPressed: () {},
                color: AppColors.appTransparentColor,
                padding: EdgeInsets.zero,
                shape: const CircleBorder(),
                child: SvgPicture.asset(
                  'assets/svgs/home-widget-empty.svg',
                  // colorFilter: ColorFilter.mode(
                  //   Color(int.parse('0xFF${themeColor.busNameColor}')),
                  //   BlendMode.srcIn,
                  // ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(24),
                child: Center(
                  child: Text(
                    isOwner ? l10n.homeEmptyOwner : l10n.homeEmptyStaff,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Color(int.parse('0xFF${themeColor?.busNameColor ?? 'FFFFFF'}')),
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ],
          ),
        )
      ];
    }
    final widgets = widgetsData.map((e) {
      return _buildListWidget(context, (e.id ?? '').toUpperCase(), stateAuth);
    }).toList();

    return widgets;
  }

  Widget _buildListWidget(
    BuildContext context,
    String type,
    AuthState stateAuth,
  ) {
    if (type.contains(HomeReorderDefine.STICKY.name)) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
        child: AddStickyWidget(),
      );
    } else if (type.contains(HomeReorderDefine.ENGAGEMENT.name)) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
        child: EngagementWidget(),
      );
    } else if (type.contains(HomeReorderDefine.POINT_CLAIMS.name) && stateAuth.enablePointClaims == true) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
        child: PointClaimsWidget(),
      );
    } else if (type.contains(HomeReorderDefine.CALL_BUTTON_SESSION.name) && stateAuth.enableCallButton == true && stateAuth.checkRole(PermissionBusiness.cb_manage_customers_requests.name)) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
        // child: HomeCallButtonPage(),
        child: HomeCallButtonPageVer2(),
      );
    } else if (type.contains(HomeReorderDefine.CUSTOMERS.name)) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
        child: FindOrScanWidget(),
      );
    } else if (type.contains(HomeReorderDefine.VOUCHERS_CALENDAR.name)) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
        child: HomeVouchersCalendarWidget(),
      );
    } else if (type.contains(HomeReorderDefine.ACTIVE_ORDERS.name) && stateAuth.checkRole(PermissionBusiness.orders_active_orders.name)) {
      return const Padding(
        padding: EdgeInsets.only(top: 16.0),
        child: HomeOrdersActiveMultiWidget(),
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  AppBar _appBar(BuildContext context, Color colorAppBar, Color colorStatusBar) {
    return AppBar(
      backgroundColor: colorStatusBar.withValues(alpha: 0),
      elevation: 0,
      leadingWidth: 0,
      title: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, stateAuth) {
          final nameBusiness = stateAuth.nameBusiness ?? '';
          return BusinessProfileItemWidget(
            isBorder: false,
            onTap: () => null,
            // icon: 'assets/svgs/edit.svg',
            linkAvatar: stateAuth.logoBusiness,
            titleValue: nameBusiness,
            fontWeight: FontWeight.w700,
            fontSize: 16,
            textColor: colorAppBar,
          );
        },
      ),
      actions: [
        Padding(
          key: widget.menuButtonKey,
          padding: const EdgeInsets.all(10.0),
          child: SizedBox(
            width: 40,
            child: MaterialButton(
              elevation: 0,
              highlightElevation: 0,
              hoverElevation: 0,
              hoverColor: AppColors.appTransparentColor,
              onPressed: () {
                FocusManager.instance.primaryFocus?.unfocus();
                AppBased.go(context, AppRoutes.settings);
              },
              color: AppColors.appTransparentColor,
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
              child: SvgPicture.asset(
                'assets/svgs/menu.svg',
                colorFilter: ColorFilter.mode(
                  colorAppBar,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _onRefresh(BuildContext context) async {
    AppLog.e('home _onRefresh');

    try {
      // if (context.read<HomeDefaultWidgetCubit>().state.isEngagementAllHome) {
      context.read<QrStatisticsCubit>().onBusinessStatistics();
      // }
      // if (context.read<HomeDefaultWidgetCubit>().state.isCallButtonAllHome) {
      context.read<CallButtonCubit>().getSections(withLoading: false);
      // }

      // if (context.read<HomeDefaultWidgetCubit>().state.isPointClaimsAllHome) {
      context.read<PointClaimsCubit>().getPointClaims(isLoading: false);
      // }
      // if (context.read<HomeDefaultWidgetCubit>().state.isVoucherCalendarAllHome) {
      context.read<VoucherCalendarCubit>().getVoucherCalendar();
      // }
      // // if (context.read<HomeDefaultWidgetCubit>().state.isVoucherCalendarAllHome) {
      context.read<OrdersActiveCubit>().getOrdersActive(loading: true); // // }
    } catch (e) {
      AppLog.e('home _onRefresh err: $e');
    }
  }
}
