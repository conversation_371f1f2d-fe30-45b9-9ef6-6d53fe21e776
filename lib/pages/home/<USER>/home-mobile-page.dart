// ignore_for_file: must_be_immutable, unawaited_futures

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-default/home-default.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-screen-tabs/home-screen-tabs.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@core/store/point-claims/point-claims.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-statistics/qr_statistics.cubit.dart';
import 'package:stickyqrbusiness/@core/store/setting-accept-online-ordering/setting-accept-online-ordering.cubit.dart';
import 'package:stickyqrbusiness/@core/store/vouchers-calendar/voucher-calendar.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/home-engagement/home-engagement-widget.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/home-vouchers-calendar-widget.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/order-active-home-widget.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/point-claims/point-claims.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/widgets/@container-widget.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home-call-button-v2/home-call-button-v2-page.dart';
// import 'package:stickyqrbusiness/pages/home-call-button/home-call-button-page.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/@home-widgets.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';

class HomeMobilePage extends StatelessWidget {
  final GlobalKey menuButtonKey;

  const HomeMobilePage({super.key, required this.menuButtonKey});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      buildWhen: (previous, current) {
        return previous.isNewHomePage != current.isNewHomePage && current.isNewHomePage != null;
      },
      builder: (context, state) {
        final isNewHomePage = state.isNewHomePage;
        AppLog.e('isNewHomePage: $isNewHomePage');
        // Nếu chưa có giá trị, show loading
        // if (isNewHomePage == null) {
        //   return const Scaffold(
        //     body: SizedBox.square(),
        //   );
        // }

        if (isNewHomePage == true) {
          return Responsive(
            mobile: HomeMobileNewPage(menuButtonKey: menuButtonKey, newHomePageDefaultPage: state.newHomePageDefaultPage),
            tablet: HomeMobileNewPage(menuButtonKey: menuButtonKey, newHomePageDefaultPage: state.newHomePageDefaultPage),
            desktop: HomeMobileNewPage(menuButtonKey: menuButtonKey, newHomePageDefaultPage: state.newHomePageDefaultPage),
          );
        } else {
          return Responsive(
            mobile: HomeMobileMultiPage(menuButtonKey: menuButtonKey),
            tablet: HomeMobileMultiPage(menuButtonKey: menuButtonKey),
            desktop: HomeMobileMultiPage(menuButtonKey: menuButtonKey),
          );
        }
      },
    );
  }
}

class HomeMobileNewPage extends StatefulWidget {
  GlobalKey menuButtonKey;
  final String? newHomePageDefaultPage;
  HomeMobileNewPage({super.key, required this.menuButtonKey, required this.newHomePageDefaultPage});

  @override
  State<HomeMobileNewPage> createState() => _HomeMobileNewPageState();
}

class _HomeMobileNewPageState extends State<HomeMobileNewPage> with WidgetsBindingObserver {
  final GlobalKey<PopupMenuButtonState<HomeSceenTab>> _popupMenuKey = GlobalKey<PopupMenuButtonState<HomeSceenTab>>();
  // Thêm biến boolean để track trạng thái popup
  bool _isPopupOpen = false;

  @override
  void initState() {
    super.initState();
    final newHomePageDefaultPage = widget.newHomePageDefaultPage ?? 'rewards';
    context.read<HomeSceenTabsCubit>().onChangedDetectTab(newHomePageDefaultPage);
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HomeDefaultWidgetCubit, HomeDefaultWidgetState>(
      listener: (context, stateWidget) {},
      builder: (context, stateWidget) {
        return GestureDetector(
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: AnnotatedRegion<SystemUiOverlayStyle>(
            value: SystemUiOverlayStyle.dark,
            child: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
                return Scaffold(
                  backgroundColor: AppColors.appBlackColor,
                  extendBody: true,
                  appBar: _appBar(context),
                  body: SafeArea(
                    bottom: false,
                    child: _buildHomeContent(context),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  // void onChangedDetectTab(String? value) {
  //   if (value?.toLowerCase() == 'active-orders') {
  //     emit(state.copyWith(tab: HomeSceenTab.ACTIVE_ORDERS));
  //   } else if (value?.toLowerCase() == 'call-button') {
  //     emit(state.copyWith(tab: HomeSceenTab.CALL_BUTTON));
  //   } else {
  //     emit(state.copyWith(tab: HomeSceenTab.REWARDS));
  //   }
  // }
  Widget _buildHomeContent(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        // final newHomePageDefaultPage = stateAuth.newHomePageDefaultPage;
        final isActiveOrder = (stateAuth.orderAllowOnlineOrdering ?? false) && stateAuth.checkRole(PermissionBusiness.orders_active_orders.name);
        final isCallButton = (stateAuth.enableCallButton ?? false) && stateAuth.checkRole(PermissionBusiness.cb_manage_customers_requests.name);
        return BlocBuilder<HomeSceenTabsCubit, HomeSceenTabsState>(
          builder: (context, stateTab) {
            final tab = stateTab.tab;

            return GestureDetector(
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              child: RefreshIndicator(
                  color: AppColors.appColor,
                  onRefresh: () => _onRefresh(context),
                  child: CustomScrollView(
                    primary: true,
                    shrinkWrap: true,
                    physics: const AlwaysScrollableScrollPhysics(
                      parent: ClampingScrollPhysics(),
                    ),
                    slivers: [
                      SliverFillRemaining(
                        child: BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, stateAuth) {
                            if (tab == HomeSceenTab.ACTIVE_ORDERS && isActiveOrder) {
                              return const HomeOrdersActiveWidget();
                            } else if (tab == HomeSceenTab.CALL_BUTTON && isCallButton) {
                              return ListView(
                                physics: const ClampingScrollPhysics(),
                                padding: const EdgeInsets.only(top: 16, bottom: 64, left: 16, right: 16),
                                children: const [HomeCallButtonPageVer2()],
                                // children: const [SizedBox.shrink()],
                              );
                            } else if (tab == HomeSceenTab.REWARDS) {
                              return _buildRewardsWidgets(stateAuth);
                            } else {
                              return _buildRewardsWidgets(stateAuth);
                            }
                          },
                        ),
                      ),
                    ],
                  )),
            );
          },
        );
      },
    );
  }

  AppBar _appBar(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;
    final l10n = context.l10n;
    return AppBar(
      backgroundColor: AppColors.appBlackColor,
      elevation: 0,
      leadingWidth: 0,
      title: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          // Khi AuthState thay đổi (có thể ảnh hưởng đến _getAvailableTabs), đóng PopupMenuButton
          if (_popupMenuKey.currentState != null && _isPopupOpen) {
            // Sử dụng Future.microtask để tránh lỗi context
            Future.microtask(() {
              if (mounted && _popupMenuKey.currentState != null) {
                // Đóng popup menu bằng cách unfocus
                // _popupMenuKey.currentState?.deactivate();
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }
                _isPopupOpen = false;
              }
            });
          }
        },
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, stateAuth) {
            final nameBusiness = stateAuth.nameBusiness ?? '';
            final isActiveOrder = (stateAuth.orderAllowOnlineOrdering ?? false) && stateAuth.checkRole(PermissionBusiness.orders_active_orders.name);
            final isCallButton = (stateAuth.enableCallButton ?? false) && stateAuth.checkRole(PermissionBusiness.cb_manage_customers_requests.name);
            return BlocBuilder<HomeSceenTabsCubit, HomeSceenTabsState>(
              builder: (context, state) {
                HomeSceenTab tab = _getAllTabs(isCallButton, isActiveOrder).contains(state.tab) ? state.tab : HomeSceenTab.REWARDS;
                if (!isActiveOrder && !isCallButton) {
                  tab = HomeSceenTab.REWARDS;
                }

                return Row(
                  children: [
                    Expanded(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Container(
                                // margin: const EdgeInsets.only(top: 24),
                                alignment: Alignment.center,
                                child: AvatarControlWidget(
                                  boxFit: BoxFit.contain,
                                  width: 40,
                                  height: 40,
                                  name: nameBusiness,
                                  urlImage: stateAuth.logoBusiness ?? '',
                                  borderRadius: (stateAuth.logoBusiness == '' || stateAuth.logoBusiness == null) ? 100 : 4,
                                  textColor: AppColors.lightPrimaryBackgroundColor,
                                  backgroundColor: (stateAuth.logoBusiness == '' || stateAuth.logoBusiness == null) ? AppColors.appColor : AppColors.appTransparentColor,
                                ),
                              ),
                              dropDownHomeScreen(
                                l10n,
                                isDisable: isTablet,
                                list: _getAvailableTabs(tab, isCallButton, isActiveOrder),
                                valueSelected: tab,
                                isCallButton: isCallButton,
                                isActiveOrder: isActiveOrder,
                                onChanged: (tabChanged) {
                                  context.read<HomeSceenTabsCubit>().onChangedTab(tabChanged);
                                },
                              ),
                            ],
                          ),
                          if (tab == HomeSceenTab.ACTIVE_ORDERS && isActiveOrder) _buildAcceptOnlineOrdering(l10n)
                        ],
                      ),
                    ),
                    if (isTablet)
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (tab != HomeSceenTab.REWARDS)
                            _buildFeature(
                              l10n.rewards,
                              onTap: () => context.read<HomeSceenTabsCubit>().onChangedTab(HomeSceenTab.REWARDS),
                            ),
                          if (tab != HomeSceenTab.CALL_BUTTON && isCallButton)
                            _buildFeature(
                              l10n.callButton,
                              onTap: () => context.read<HomeSceenTabsCubit>().onChangedTab(HomeSceenTab.CALL_BUTTON),
                            ),
                          if (tab != HomeSceenTab.ACTIVE_ORDERS && isActiveOrder)
                            _buildFeature(
                              l10n.activeOrders,
                              onTap: () => context.read<HomeSceenTabsCubit>().onChangedTab(HomeSceenTab.ACTIVE_ORDERS),
                            ),
                        ],
                      )
                  ],
                );
              },
            );
          },
        ),
      ),
      actions: [
        Padding(
          key: widget.menuButtonKey,
          padding: const EdgeInsets.all(2),
          child: SizedBox(
            width: 40,
            child: MaterialButton(
              elevation: 0,
              highlightElevation: 0,
              hoverElevation: 0,
              hoverColor: AppColors.appTransparentColor,
              onPressed: () {
                FocusManager.instance.primaryFocus?.unfocus();
                AppBased.go(context, AppRoutes.settings);
              },
              color: AppColors.appTransparentColor,
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
              child: SvgPicture.asset(
                'assets/svgs/menu.svg',
                colorFilter: const ColorFilter.mode(
                  AppColors.lightPrimaryBackgroundColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget dropDownHomeScreen(
    AppLocalizations l10n, {
    bool isDisable = false,
    List<HomeSceenTab>? list,
    HomeSceenTab? valueSelected,
    ValueChanged<HomeSceenTab>? onChanged,
    bool isCallButton = false,
    bool isActiveOrder = false,
  }) {
    final validValueSelected = _getAllTabs(isCallButton, isActiveOrder).contains(valueSelected) ? valueSelected! : HomeSceenTab.REWARDS;

    return AbsorbPointer(
      absorbing: isDisable,
      child: Theme(
        data: Theme.of(context).copyWith(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
        ),
        child: SizedBox(
          height: 48,
          child: Builder(
            builder: (context) {
              return PopupMenuButton<HomeSceenTab>(
                key: _popupMenuKey, // Sử dụng GlobalKey để điều khiển
                offset: Offset(0, AppBar().preferredSize.height),
                onSelected: (valueChange) {
                  onChanged?.call(valueChange);
                  _isPopupOpen = false;
                },
                onCanceled: () {
                  _isPopupOpen = false;
                },
                onOpened: () {
                  _isPopupOpen = true;
                },
                child: Container(
                  padding: const EdgeInsets.only(left: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _detectTitleFeatureEnable(l10n, validValueSelected),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.lightPrimaryBackgroundColor,
                        ),
                      ),
                      if (!isDisable)
                        Padding(
                          padding: const EdgeInsets.only(left: 4),
                          child: SvgPicture.asset(
                            'assets/svgs/arrow-down-icon.svg',
                            colorFilter: const ColorFilter.mode(
                              AppColors.lightPrimaryBackgroundColor,
                              BlendMode.srcIn,
                            ),
                            // width: 16,
                            // height: 16,
                          ),
                        )
                    ],
                  ),
                ),
                itemBuilder: (_) => (list ?? []).map((HomeSceenTab value) {
                  return PopupMenuItem(
                    value: value,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(right: 12),
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(99),
                            color: AppColors.anouncementBGColor,
                          ),
                          child: SvgPicture.asset(
                            detectIconWidget(value),
                            colorFilter: const ColorFilter.mode(
                              AppColors.appBlackColor,
                              BlendMode.srcIn,
                            ),
                            width: 20,
                            height: 20,
                          ),
                        ),
                        Text(
                          _detectTitleFeatureEnable(l10n, value),
                          style: const TextStyle(
                            fontSize: 16,
                            height: 1.4,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              );
            },
          ),
        ),
      ),
    );
  }

  String detectIconWidget(HomeSceenTab tab) {
    if (tab == HomeSceenTab.ACTIVE_ORDERS) {
      return 'assets/svgs/order-cart.svg';
    } else if (tab == HomeSceenTab.CALL_BUTTON) {
      return 'assets/svgs/call-button.svg';
    } else if (tab == HomeSceenTab.REWARDS) {
      return 'assets/svgs/promotion.svg';
    } else {
      return 'assets/svgs/promotion.svg';
    }
  }

  String _detectTitleFeatureEnable(AppLocalizations l10n, HomeSceenTab tab) {
    if (tab == HomeSceenTab.ACTIVE_ORDERS) {
      return l10n.activeOrders;
    } else if (tab == HomeSceenTab.CALL_BUTTON) {
      return l10n.callButton;
    } else if (tab == HomeSceenTab.REWARDS) {
      return l10n.rewards;
    } else {
      return '';
    }
  }

  Widget _buildAcceptOnlineOrdering(AppLocalizations l10n) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        // final isAcceptOnlineOrdering = state.business?.orderAllowOnlineOrdering ?? false;
        final business = stateAuth.business;
        final timeZone = context.read<AuthBloc>().state.businessTimeZone ?? '';
        final isAcceptOnlineOrdering = business?.orderAllowOnlineOrdering ?? false;
        final storeStt = business?.orderOperationStatus;
        final storeOperationSttUntil = business?.orderOperationStatusUntil;
        final statusOnline = getStoreStatus(
          context: context,
          l10n: l10n,
          timezone: timeZone,
          acceptOnlineOrdering: isAcceptOnlineOrdering,
          stt: storeStt ?? '',
          operationStatusUntil: storeOperationSttUntil,
        );

        return BlocProvider(
          create: (context) => SettingOrderAcceptOnlineOrderingCubit()..onChangeToggle(isAcceptOnlineOrdering),
          child: BlocConsumer<SettingOrderAcceptOnlineOrderingCubit, SettingOrderAcceptOnlineOrderingState>(
            listener: (context, state) {
              final stt = state.status;
              switch (stt) {
                case AcceptOnlineOrderingStatus.Success:
                  break;
                case AcceptOnlineOrderingStatus.Error:
                  if (state.errorMsg?.isNotEmpty == true) {
                    AppBased.toastError(context, title: state.errorMsg);
                  }
                  break;
                default:
                  break;
              }
            },
            builder: (context, state) {
              return AbsorbPointer(
                absorbing: !stateAuth.checkRole(PermissionBusiness.orders_orders_settings.name),
                child: InkWell(
                  onTap: () {
                    AppBased.go(context, AppRoutes.updateStoreStatus);
                  },
                  borderRadius: BorderRadius.circular(99),
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.fromLTRB(8, 6, 8, 6),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: getStoreSttColor(storeStt ?? '').withValues(alpha: .2),
                      borderRadius: BorderRadius.circular(99),
                      border: Border.all(
                        width: 1.2,
                        color: getStoreSttColor(storeStt ?? ''),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: !isAcceptOnlineOrdering ? const Color(0xFF595D62) : getStoreSttColor(storeStt ?? ''),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          statusOnline,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Color getStoreSttColor(String stt) {
    switch (stt.toUpperCase()) {
      case 'ACTIVE':
        return Colors.green;
      case 'BUSY':
        return Colors.orange;
      case 'PAUSED':
        return Colors.red;
      default:
        return Colors.green;
    }
  }

  String getStoreStatus({
    required BuildContext context,
    required AppLocalizations l10n,
    required String timezone,
    required bool acceptOnlineOrdering,
    required String stt,
    DateTime? operationStatusUntil,
  }) {
    // final time = DateTimeHelper.timeFormat(context, operationStatusUntil, timeZone: timezone);

    if (!acceptOnlineOrdering) {
      return l10n.closed;
    }

    switch (stt.toUpperCase()) {
      case 'ACTIVE':
        if (operationStatusUntil != null) {
          return l10n.onlineActive;
          // return '${l10n.acceptingOrdersUntil} $time';
        }
        return l10n.onlineActive;
      // return l10n.acceptOnlineOrdering;
      case 'BUSY':
        return l10n.busy;
      // return '${l10n.acceptingOrdersUntil} $time';
      case 'PAUSED':
        return l10n.paused;
      // return '${l10n.pauseAcceptingOrdersUntil} $time';

      default:
        return l10n.onlineActive;
      // return l10n.acceptOnlineOrdering;
    }
  }

  Widget _buildRewardsWidgets(AuthState stateAuth) {
    return ListView(
      physics: const ClampingScrollPhysics(),
      padding: const EdgeInsets.only(bottom: 64, left: 16, right: 16),
      children: [
        const Padding(
          padding: EdgeInsets.only(top: 16.0),
          child: EngagementWidget(),
        ),
        const Padding(
          padding: EdgeInsets.only(top: 16.0),
          child: FindOrScanWidget(),
        ),
        const Padding(
          padding: EdgeInsets.only(top: 16.0),
          child: AddStickyWidget(),
        ),
        if (stateAuth.enablePointClaims == true && stateAuth.checkRole(PermissionBusiness.point_claims.name)) ...[
          const Padding(
            padding: EdgeInsets.only(top: 16.0),
            child: PointClaimsWidget(),
          ),
        ],
        if (stateAuth.checkRole(PermissionBusiness.offers.name)) ...[
          const Padding(
            padding: EdgeInsets.only(top: 16.0),
            child: HomeVouchersCalendarWidget(),
          ),
        ],
      ],
    );
  }

  Widget _buildFeature(String title, {TextStyle? style, Function? onTap}) {
    return InkWell(
      radius: 8,
      borderRadius: BorderRadius.circular(8),
      onTap: () => onTap?.call(),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
        child: Text(
          title,
          style: style ??
              const TextStyle(
                fontSize: 16,
                color: AppColors.lightPrimaryBackgroundColor,
                fontWeight: FontWeight.w600,
              ),
        ),
      ),
    );
  }

  Future<void> _onRefresh(BuildContext context) async {
    try {
      // if (context.read<HomeDefaultWidgetCubit>().state.isEngagementAllHome) {
      context.read<QrStatisticsCubit>().onBusinessStatistics();
      // }
      // if (context.read<HomeDefaultWidgetCubit>().state.isCallButtonAllHome) {
      context.read<CallButtonCubit>().getSections(withLoading: false);
      // }

      // if (context.read<HomeDefaultWidgetCubit>().state.isPointClaimsAllHome) {
      context.read<PointClaimsCubit>().getPointClaims(isLoading: false);
      // }
      // if (context.read<HomeDefaultWidgetCubit>().state.isVoucherCalendarAllHome) {
      context.read<VoucherCalendarCubit>().getVoucherCalendar();
      // }
      // // if (context.read<HomeDefaultWidgetCubit>().state.isVoucherCalendarAllHome) {
      context.read<OrdersActiveCubit>().getOrdersActive(loading: true); // // }
    } catch (e) {
      AppLog.e('home _onRefresh err: $e');
    }
  }

  List<HomeSceenTab> _getAvailableTabs(HomeSceenTab currentTab, bool isCallButton, bool isActiveOrder) {
    final List<HomeSceenTab> availableTabs = [];

    if (currentTab != HomeSceenTab.REWARDS) {
      availableTabs.add(HomeSceenTab.REWARDS);
    }

    if (currentTab != HomeSceenTab.CALL_BUTTON && isCallButton) {
      availableTabs.add(HomeSceenTab.CALL_BUTTON);
    }

    if (currentTab != HomeSceenTab.ACTIVE_ORDERS && isActiveOrder) {
      availableTabs.add(HomeSceenTab.ACTIVE_ORDERS);
    }

    return availableTabs;
  }

  List<HomeSceenTab> _getAllTabs(bool isCallButton, bool isActiveOrder) {
    final List<HomeSceenTab> availableTabs = [HomeSceenTab.REWARDS];

    if (isCallButton) {
      availableTabs.add(HomeSceenTab.CALL_BUTTON);
    }
    if (isActiveOrder) {
      availableTabs.add(HomeSceenTab.ACTIVE_ORDERS);
    }

    return availableTabs;
  }
}
