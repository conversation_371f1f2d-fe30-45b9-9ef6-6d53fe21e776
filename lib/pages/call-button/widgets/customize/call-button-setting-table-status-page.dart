// ignore_for_file: avoid_positional_boolean_parameters

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/app-based.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/cb-setting-table-status/cb-setting-table-status.cubit.dart';
import 'package:stickyqrbusiness/@widgets/button-loading-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class CBSettingTableStatusPage extends StatelessWidget {
  const CBSettingTableStatusPage({super.key});

  @override
  Widget build(BuildContext context) {
    final business = context.read<AuthBloc>().state.business;
    final inprogressTime = business?.cbTableStatusSettings?.inProgressChangeAfterMinutes ?? 30;
    final attentionRequiredTime = business?.cbTableStatusSettings?.attentionRequiredChangeAfterMinutes ?? 60;
    final inProgress = business?.cbTableStatusSettings?.enableInProgress ?? false;
    final attentionRequired = business?.cbTableStatusSettings?.enableAttentionRequired ?? false;
    return BlocProvider(
      create: (context) => CBSettingTableStatusCubit()
        ..onEnableInProgress(inProgress)
        ..onEnableAttentionRequired(attentionRequired)
        ..onChangedInProgressTime(inprogressTime)
        ..onEnableAttentionRequired(attentionRequired)
        ..onChangedAttentionRequiredTime(attentionRequiredTime),
      child: const CBSettingTableStatusView(),
    );
  }
}

class CBSettingTableStatusView extends StatefulWidget {
  const CBSettingTableStatusView({super.key});

  @override
  State<CBSettingTableStatusView> createState() => _CBSettingTableStatusState();
}

class _CBSettingTableStatusState extends State<CBSettingTableStatusView>
    with WidgetsBindingObserver {
  late final l10n = context.l10n;

  List<int> get allDurations {
    final List<int> durations = [];
    for (int i = 5; i <= 120; i += 5) {
      durations.add(i);
    }
    return durations;
  }
  
  List<int> get durationsChangeAfter => allDurations;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CBSettingTableStatusCubit, CBSettingTableStatusState>(
      listener: (context, state) {
        switch (state.status) {
          case CBSettingTableStatus.Error:
            if (state.errorMsg != null && state.errorMsg != '') {
              AppBased.toastError(context, title: state.errorMsg);
              context.read<CBSettingTableStatusCubit>().onResetStatus();
            }
            return;
          case CBSettingTableStatus.Success:
            AppBased.toastSuccess(context, title: l10n.successfully);
            Navigator.pop(context, true);
            return;
          default:
        }
      },
      child: Scaffold(
          appBar: _buildAppbar(),
          body: SafeArea(
            child: _buildBody(),
          ),
        ),
    );
  }

  AppBar _buildAppbar() {
    return AppBar(
      automaticallyImplyLeading: false,
      centerTitle: true,
      titleSpacing: 0.0,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leading: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SizedBox(
          width: 40,
          child: MaterialButton(
            elevation: 0,
            highlightElevation: 0,
            hoverElevation: 0,
            hoverColor: AppColors.lightPrimaryBackgroundColor,
            onPressed: () => {
              Navigator.of(context).pop(),
            },
            color: AppColors.lightPrimaryBackgroundColor,
            padding: EdgeInsets.zero,
            shape: const CircleBorder(),
            child: SvgPicture.asset(
              'assets/svgs/arrow-back.svg',
            ),
          ),
        ),
      ),
      title: Text(
        l10n.tableStatus,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        Expanded(child: _buildContent()),
        _buildActionsBtns(),
      ],
    );
  }

  Widget _buildContent() {
    return BlocBuilder<CBSettingTableStatusCubit, CBSettingTableStatusState>(
      builder: (context, state) {
        final inProgress = state.inProgress;
        final attentionRequired = state.attentionRequired;
        final inProgressTime = state.inProgressTime;
        final attentionRequiredTime = state.attentionRequiredTime;
        final isShowErrorMsg = state.isValidateChangeAfter;

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTitle(),
                _buildItemSelected(
                  isDefault: true,
                  isShowChangeAfter: false,
                  title: l10n.justSeated,
                  isChecked: true,
                  color: const Color(0xFFE7EFFB),
                  borderColor: const Color(0xFF6777F8),
                ),
                _buildDivider(),
                _buildItemSelected(
                  isShowChangeAfter: true,
                  title: l10n.inProgressOrder,
                  isChecked: inProgress,
                  color: const Color(0xFFFFF1D4),
                  borderColor: const Color(0xFFB3804A),
                  onChecked: (p0) => context.read<CBSettingTableStatusCubit>().onEnableInProgress(p0),
                  durations: durationsChangeAfter,
                  selectedDuration: inProgressTime,
                  onChangeAfterFunc: (value) => context.read<CBSettingTableStatusCubit>().onChangedInProgressTime(value),
                ),
                _buildDivider(),
                _buildItemSelected(
                  isShowChangeAfter: true,
                  title: l10n.attentionRequired,
                  isChecked: attentionRequired,
                  color: const Color(0xFFFFE4E7),
                  borderColor: const Color(0xFFD33030),
                  onChecked: (p0) => context.read<CBSettingTableStatusCubit>().onEnableAttentionRequired(p0),
                  durations: durationsChangeAfter,
                  selectedDuration: attentionRequiredTime,
                  onChangeAfterFunc: (value) => context.read<CBSettingTableStatusCubit>().onChangedAttentionRequiredTime(value),
                  isError: isShowErrorMsg,
                ),
                if (isShowErrorMsg) ... [
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: RichText(
                      textAlign: TextAlign.left,
                      text: TextSpan(
                        children: <InlineSpan>[
                          const WidgetSpan(
                            alignment: PlaceholderAlignment.middle,
                            child: Icon(
                              Icons.warning_amber_rounded,
                              color: Colors.red,
                              size: 16,
                            ),
                          ),
                          TextSpan(
                            text: ' ${l10n.important}: ',
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              height: 1.4,
                            ),
                          ),
                          TextSpan(
                            // text: "The 'Attention Required' duration must be longer than the 'In Progress' duration.",
                            text: l10n.theDurationOfAttentionRequired,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 13,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTitle() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.enableTableStatus,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          l10n.allowsYouToSetTablesToAutomaticallyChangeColorBasedOnHowLongGuestsHaveBeenSeated,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.normal,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  Widget _buildItemSelected({
    required String title,
    required bool isChecked,
    required Color color,
    required Color borderColor,
    List<int>? durations,
    Function(bool)? onChecked,
    Function(int)? onChangeAfterFunc,
    int? selectedDuration,
    bool isShowChangeAfter = true,
    bool isDefault = false,
    bool isError = false,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Transform.translate(
          offset: const Offset(-12, 0),
          child: GestureDetector(
            onTap: isDefault
                ? null
                : () {
                    if (onChecked != null) {
                      onChecked(!isChecked); // Toggle trạng thái
                    }
                  },
            child: Row(
              children: [
                Opacity(
                  opacity: isDefault ? .4 : 1,
                  child: Transform.scale(
                    scale: 1,
                    child: Checkbox(
                      value: isDefault ? true : isChecked,
                      onChanged: isDefault
                          ? null
                          : (bool? value) {
                              if (onChecked != null && value != null) {
                                onChecked(value);
                              }
                            },
                      checkColor: Colors.white,
                      fillColor: WidgetStateProperty.resolveWith<Color>(
                        (Set<WidgetState> states) {
                          if (states.contains(WidgetState.selected)) {
                            return Colors.black;
                          }
                          return Colors.white;
                        },
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      side: BorderSide(
                        color: Colors.grey[400]!,
                        width: 2,
                      ),
                    ),
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ),
        Opacity(
          opacity: isChecked ? 1 : .4,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                l10n.statusColor,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: Color(0xFF595D62),
                ),
              ),
              Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(left: 16),
                decoration: BoxDecoration(
                  border: Border.all(
                    width: 1,
                    color: borderColor,
                  ),
                  color: color,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (isShowChangeAfter)
          Opacity(
            opacity: isChecked ? 1 : .4,
            child: Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    l10n.changeAfter,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.normal,
                      color: Color(0xFF595D62),
                    ),
                  ),
                  Container(
                    width: MediaQuery.sizeOf(context).width * 0.45,
                    padding: const EdgeInsets.only(left: 12, right: 8),
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: isError ? Colors.red : Colors.grey.shade300),
                    ),
                    // child: DropdownButtonHideUnderline(
                    //   child: DropdownButton<int>(
                    //     value: selectedDuration,
                    //     isExpanded: true,
                    //     style: const TextStyle(
                    //       fontSize: 16,
                    //       fontWeight: FontWeight.normal,
                    //       color: Colors.black,
                    //     ),
                    //     icon: Icon(
                    //       Icons.keyboard_arrow_down,
                    //       size: 28,
                    //       color: Colors.grey.shade600,
                    //     ),
                    //     onChanged: !isChecked ? null : (int? newValue) {
                    //       if (newValue != null) {
                    //         if (onChangeAfterFunc != null) {
                    //           onChangeAfterFunc(newValue);
                    //         }
                    //       }
                    //     },
                    //     items: durations?.map<DropdownMenuItem<int>>((int? value) {
                    //       return DropdownMenuItem<int>(
                    //         value: value,
                    //         child: Text('$value ${l10n.mins}'),
                    //       );
                    //     }).toList(),
                    //   ),
                    // ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<int>(
                        value: selectedDuration,
                        isExpanded: true,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.normal,
                          color: !isChecked ? Colors.grey : Colors.black,
                        ),
                        icon: Icon(
                          Icons.keyboard_arrow_down,
                          size: 28,
                          color: !isChecked ? Colors.grey.shade400 : Colors.grey.shade600,
                        ),
                        onChanged: !isChecked ? null : (int? newValue) {
                          if (newValue != null) {
                            if (onChangeAfterFunc != null) {
                              onChangeAfterFunc(newValue);
                            }
                          }
                        },
                        selectedItemBuilder: (context) {
                          return durations?.map<Widget>((int? value) {
                            return Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '$value ${l10n.mins}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.normal,
                                  color: Colors.black,
                                ),
                              ),
                            );
                          }).toList() ?? [];
                        },
                        items: durations?.map<DropdownMenuItem<int>>((int? value) {
                          final isSelected = value == selectedDuration;
                          return DropdownMenuItem<int>(
                            value: value,
                            child: Text(
                              '$value ${l10n.mins}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                color: isSelected ? Colors.black : Colors.black87,
                              ),
                            ),
                          );
                        }).toList(),
                        menuMaxHeight: MediaQuery.sizeOf(context).height * 0.6,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionsBtns() {
    return BlocBuilder<CBSettingTableStatusCubit, CBSettingTableStatusState>(
      builder: (context, state) {
        final stt = state.status;
        return Container(
          padding: EdgeInsets.fromLTRB(16, 12, 16, Platform.isIOS ? 0 : 12),
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                width: 1,
                color: Colors.grey.shade300,
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: ButtonLoading(
                  callback: () {
                    context.read<CBSettingTableStatusCubit>().onUpdateCallButtonTableStatus();
                  },
                  height: 48,
                  borderRadius: 8,
                  buttonBackgroundColor: AppColors.appColor,
                  label: l10n.save,
                  labelColor: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  // side: border,
                  isLoading: stt == CBSettingTableStatus.Loading,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDivider() {
    return const Padding(
      padding: EdgeInsets.only(
        top: 24.0,
        bottom: 8.0,
      ),
      child: Divider(
        height: 1,
        color: Color(0xFFEBEBEB),
      ),
    );
  }

  void _buildCompleted() {}
}
