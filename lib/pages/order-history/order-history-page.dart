// ignore_for_file: unawaited_futures

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/orders-history/orders-history.cubit.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';
import 'package:stickyqrbusiness/@widgets/date-range-custom/date_ranger.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/order-history/widgets/@order-history-widgets.dart';
import 'package:stickyqrbusiness/pages/ordering-management-product/widgets/categories-empty-widget.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/order-define.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/orders-active-drawer-widget.dart';

class OrderHistoryScreen extends StatelessWidget {
  const OrderHistoryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool isFromHome = false;
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is ScreenArguments) {
      final data = args.data as bool;
      isFromHome = data;
    }
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (value, f) {
        return;
      },
      child: BlocProvider(
        create: (_) => OrderHistoryCubit()..getOrders(page: 0),
        child: OrderHistoryView(isFromHome: isFromHome),
      ),
    );
  }
}

class OrderHistoryView extends StatefulWidget {
  final bool isFromHome;
  const OrderHistoryView({Key? key, this.isFromHome = false}) : super(key: key);

  @override
  State<OrderHistoryView> createState() => _OrderHistoryViewState();
}

class _OrderHistoryViewState extends State<OrderHistoryView> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final bool isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      key: _scaffoldKey,
      appBar: _buildAppbar(context, l10n),
      endDrawer: OrdersActiveDrawerWidget(
        activePage: OrderDrawerPageActive.OrdersHistory,
        isFromHome: widget.isFromHome,
      ),
      body: _buildBody(context, l10n, isTablet),
    );
  }

  AppBar _buildAppbar(BuildContext context, AppLocalizations l10n) {
    final bool isTablet = MediaQuery.of(context).size.width >= 600;
    final timeZone = context.read<AuthBloc>().state.businessTimeZone ?? '';
    final items = [
      _buildSearch(l10n),
      _buildFilterDateTime(l10n, timeZone),
      _buildFilterStatus(l10n),
    ];
    return AppBar(
        elevation: 1,
        scrolledUnderElevation: 1,
        shadowColor: Colors.grey.withValues(alpha: .2),
        automaticallyImplyLeading: false,
        backgroundColor: AppColors.lightPrimaryBackgroundColor,
        centerTitle: true,
        title: Text(
          l10n.ordersHistory,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (widget.isFromHome) ...[
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: SizedBox(
                width: 60,
                child: MaterialButton(
                  elevation: 0,
                  highlightElevation: 0,
                  hoverElevation: 0,
                  hoverColor: AppColors.lightPrimaryBackgroundColor,
                  onPressed: () => {
                    Navigator.of(context).pop(),
                  },
                  color: AppColors.anouncementBorder.withValues(alpha: .3),
                  shape: const CircleBorder(),
                  padding: EdgeInsets.zero,
                  child: SvgPicture.asset('assets/svgs/close-black.svg'),
                ),
              ),
            )
          ] else
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: IconButton(
                splashRadius: 16,
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.appTransparentColor,
                ),
                icon: SvgPicture.asset(
                  'assets/svgs/menu.svg',
                  colorFilter: const ColorFilter.mode(
                    AppColors.appBlackColor,
                    BlendMode.srcIn,
                  ),
                  width: 32,
                  height: 32,
                ),
                onPressed: () => _scaffoldKey.currentState?.openEndDrawer(),
              ),
            )
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(isTablet ? 52 : 90),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 4, 16, 8),
            child: !isTablet ? _buildFilterMobile(items) : _buildFilterTablet(items),
          ),
        ));
  }

  Widget _buildFilterMobile(List<Widget> items) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: 42,
          child: items[0],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 42,
                child: items[1],
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: SizedBox(
                height: 42,
                child: items[2],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterTablet(List<Widget> items) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          constraints: const BoxConstraints(maxWidth: 250),
          child: items[0],
        ),
        Row(
          children: [
            Container(
              constraints: const BoxConstraints(maxWidth: 160),
              child: items[1],
            ),
            const SizedBox(width: 8),
            Container(
              constraints: const BoxConstraints(maxWidth: 180),
              child: items[2],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearch(AppLocalizations l10n) {
    return BlocBuilder<OrderHistoryCubit, OrderHistoryState>(
      builder: (context, state) {
        return TextFormField(
          controller: _searchController,
          onTapOutside: (event) {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          onChanged: (searchText) {
            context.read<OrderHistoryCubit>().onChangedSearch(searchText.trim());
          },
          textAlignVertical: TextAlignVertical.center,
          textAlign: TextAlign.start,
          decoration: InputDecoration(
            isDense: true,
            hintText: l10n.searchForAnItem,
            hintStyle: const TextStyle(
              fontSize: 16,
              color: AppColors.appLabelTextColor,
            ),
            contentPadding: const EdgeInsets.symmetric(vertical: 10),
            prefixIcon: const Padding(
              padding: EdgeInsets.only(left: 12),
              child: Icon(Icons.search, size: 24),
            ),
            prefixIconConstraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            filled: true,
            fillColor: AppColors.lightPrimaryBackgroundColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                width: 1,
                color: AppColors.appBorderColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                width: 1,
                color: AppColors.appBorderColor,
              ),
            ),
            suffixIcon: (state.searchText != '' && state.searchText != null)
                ? IconButton(
                    padding: const EdgeInsets.all(2),
                    splashRadius: 1,
                    icon: const Icon(
                      Icons.clear,
                      size: 18,
                      color: AppColors.iconColor,
                    ),
                    onPressed: () {
                      _searchController.clear();
                      context.read<OrderHistoryCubit>().onChangedSearch('');
                      setState(() {});
                    },
                  )
                : null,
          ),
        );
      },
    );
  }

  Widget _buildFilterDateTime(
    AppLocalizations l10n,
    String timeZone,
  ) {
    return BlocBuilder<OrderHistoryCubit, OrderHistoryState>(
      builder: (context, state) {
        return _buildDropDownDateTime(
          context,
          l10n,
          valueSelected: state.dateType,
          list: [
            OrderHistoryFilterDateTime.ALL,
            OrderHistoryFilterDateTime.TODAY,
            // OrderHistoryFilterDateTime.THIS_WEEK,
            // OrderHistoryFilterDateTime.THIS_MONTH,
            OrderHistoryFilterDateTime.LAST_7_DATE,
            OrderHistoryFilterDateTime.LAST_30_DATE,
            OrderHistoryFilterDateTime.LAST_3_MONTHS,
            OrderHistoryFilterDateTime.LAST_6_MONTHS,
            OrderHistoryFilterDateTime.THIS_MONTH,
            OrderHistoryFilterDateTime.CUSTOM,
          ],
          onChanged: (OrderHistoryFilterDateTime value) => selectedDateTime(
            state,
            value,
            l10n.done,
            timeZone,
          ),
        );
      },
    );
  }

  Widget _buildFilterStatus(
    AppLocalizations l10n,
  ) {
    return BlocBuilder<OrderHistoryCubit, OrderHistoryState>(
      builder: (context, state) {
        return _buildDropDownStatus(
          context,
          l10n,
          valueSelected: state.filterStatus,
          list: [
            OrderHistoryFilterStatus.ALL,
            OrderHistoryFilterStatus.COMPLETED,
            OrderHistoryFilterStatus.CANCELLED,
          ],
          onChanged: (OrderHistoryFilterStatus value) {
            context.read<OrderHistoryCubit>()
              ..onChangedStatus(value)
              ..getOrders(
                page: 0,
              );
          },
        );
      },
    );
  }

  Widget _buildDropDownDateTime(
    BuildContext context,
    AppLocalizations l10n, {
    List<OrderHistoryFilterDateTime>? list,
    OrderHistoryFilterDateTime? valueSelected,
    Function? onChanged,
  }) {
    return Theme(
      data: Theme.of(context).copyWith(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
      ),
      child: SizedBox(
        height: 48,
        child: Builder(
          builder: (context) {
            return PopupMenuButton<OrderHistoryFilterDateTime>(
              offset: Offset(0, AppBar().preferredSize.height),
              onOpened: () {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              onSelected: (valueChange) {
                onChanged?.call(valueChange);
              },
              constraints: const BoxConstraints(
                minWidth: 180,
                maxWidth: 180,
              ),
              child: Container(
                padding: const EdgeInsets.fromLTRB(8, 8, 10, 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    width: 1,
                    color: AppColors.appBorderColor,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 2),
                      child: SvgPicture.asset(
                        'assets/svgs/calendar.svg',
                        width: 24,
                        height: 24,
                        colorFilter: const ColorFilter.mode(
                          AppColors.appBlackColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        getValueDateTime(valueSelected, l10n),
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 2),
                      child: SvgPicture.asset(
                        'assets/svgs/arrow-down-icon.svg',
                        colorFilter: const ColorFilter.mode(
                          AppColors.darkPrimaryBackgroundColor,
                          BlendMode.srcIn,
                        ),
                        width: 16,
                        height: 16,
                      ),
                    )
                  ],
                ),
              ),
              itemBuilder: (_) => (list ?? []).map((OrderHistoryFilterDateTime value) {
                return PopupMenuItem(
                  value: value,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        getValueDateTime(value, l10n),
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.4,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (value == valueSelected)
                        Padding(
                          padding: const EdgeInsets.only(left: 6.0),
                          child: SvgPicture.asset(
                            'assets/svgs/check-calendar.svg',
                            colorFilter: const ColorFilter.mode(
                              AppColors.darkPrimaryBackgroundColor,
                              BlendMode.srcIn,
                            ),
                          ),
                        )
                    ],
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }

  String getValueDateTime(OrderHistoryFilterDateTime? value, AppLocalizations l10n) {
    switch (value) {
      case OrderHistoryFilterDateTime.ALL:
        return l10n.all;
      case OrderHistoryFilterDateTime.TODAY:
        return l10n.toDay;
      case OrderHistoryFilterDateTime.LAST_7_DATE:
        return l10n.last7Days;
      case OrderHistoryFilterDateTime.LAST_30_DATE:
        return l10n.last30Days;
      case OrderHistoryFilterDateTime.LAST_3_MONTHS:
        return l10n.last3Months;
      case OrderHistoryFilterDateTime.LAST_6_MONTHS:
        return l10n.last6Months;
      case OrderHistoryFilterDateTime.THIS_MONTH:
        return l10n.monthToDate;
      case OrderHistoryFilterDateTime.CUSTOM:
        return l10n.custom;
      default:
        return l10n.toDay;
    }
  }

  void selectedDateTime(
    OrderHistoryState state,
    OrderHistoryFilterDateTime value,
    String saveText,
    String timeZone,
  ) {
    context.read<OrderHistoryCubit>()
      ..onChangedDateTimeType(value)
      ..onChangedTimeZone(timeZone);
    String? dateStart;
    String? dateEnd;
    final DateTime dateTime = DateTime.now();
    if (value == OrderHistoryFilterDateTime.ALL) {
    } else if (value == OrderHistoryFilterDateTime.TODAY) {
      dateStart = DateTime(dateTime.year, dateTime.month, dateTime.day, 0, 0).toIso8601String();
      dateEnd = dateTime.toIso8601String();
    } else if (value == OrderHistoryFilterDateTime.THIS_MONTH) {
      dateStart = DateTime(dateTime.year, dateTime.month, 1, 0, 0).toIso8601String();
      dateEnd = DateTime(dateTime.year, dateTime.month + 1, 0).toIso8601String();
    } else if (value == OrderHistoryFilterDateTime.LAST_7_DATE) {
      final dateStartDetect = dateTime.subtract(const Duration(days: 6));
      dateStart = DateTime(dateStartDetect.year, dateStartDetect.month, dateStartDetect.day).toIso8601String();
      dateEnd = dateTime.toIso8601String();
    } else if (value == OrderHistoryFilterDateTime.LAST_30_DATE) {
      final dateStartDetect = dateTime.subtract(const Duration(days: 29));
      dateStart = DateTime(dateStartDetect.year, dateStartDetect.month, dateStartDetect.day).toIso8601String();
      dateEnd = dateTime.toIso8601String();
    } else if (value == OrderHistoryFilterDateTime.LAST_3_MONTHS) {
      final dateStartDetect = DateTime(dateTime.year, dateTime.month - 3, dateTime.day, 0, 0);
      dateStart = DateTime(dateStartDetect.year, dateStartDetect.month, dateStartDetect.day).toIso8601String();
      dateEnd = dateTime.toIso8601String();
    } else if (value == OrderHistoryFilterDateTime.LAST_6_MONTHS) {
      final dateStartDetect = DateTime(dateTime.year, dateTime.month - 6, dateTime.day, 0, 0);
      dateStart = DateTime(dateStartDetect.year, dateStartDetect.month, dateStartDetect.day).toIso8601String();
      dateEnd = dateTime.toIso8601String();
    } else {
      _showCalendar(
        saveText,
        state.dateStart,
        state.dateEnd,
      );
      return;
    }
    AppLog.e('DATE CHOOSE: $dateStart - $dateEnd');

    context.read<OrderHistoryCubit>()
      ..onChangedPageClear()
      ..onChangedDateFrom(dateStart)
      ..onChangedDateTo(dateEnd)
      ..getOrders(page: 0);
  }

  Future<void> _showCalendar(String saveText, String? startDate, String? endDate) async {
    AppBased.openShowModalBottomSheetSetHight(
      context,
      widget: _buildDefaultRangeDatePickerWithValue(startDate, endDate),
    );
  }

  Widget _buildDefaultRangeDatePickerWithValue(String? startDate, String? endDate) {
    return DateRanger(
      rangerType: DateRangerType.range,
      initialDate: DateTime.now(),
      initialRange: (startDate != null && endDate != null)
          ? DateTimeRange(
              start: DateTime.parse(startDate),
              end: DateTime.parse(endDate),
            )
          : null,
      minYear: 2023,
      maxYear: DateTime.now().year + 1,
      showDoubleTapInfo: false,
      isEnbeAllDay: true,
      runSpacing: 10,
      activeItemBackground: AppColors.appColor,
      onRangeChanged: (range) {},
      onAction: (DateTimeRange? range) {
        AppLog.e('range: $range');
        if (range != null) {
          final String dateFrom = range.start.toIso8601String();
          final String dateTo = range.end.toIso8601String();
          context.read<OrderHistoryCubit>()
            ..onChangedDateFrom(dateFrom)
            ..onChangedDateTo(dateTo)
            ..getOrders(page: 0);
        }
      },
    );
  }

  Widget _buildDropDownStatus(
    BuildContext context,
    AppLocalizations l10n, {
    List<OrderHistoryFilterStatus>? list,
    OrderHistoryFilterStatus? valueSelected,
    Function? onChanged,
  }) {
    return Theme(
      data: Theme.of(context).copyWith(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
      ),
      child: SizedBox(
        height: 48,
        child: Builder(
          builder: (context) {
            return PopupMenuButton<OrderHistoryFilterStatus>(
              offset: Offset(0, AppBar().preferredSize.height),
              onOpened: () {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              onSelected: (valueChange) {
                onChanged?.call(valueChange);
              },
              constraints: const BoxConstraints(
                minWidth: 180,
                maxWidth: 180,
              ),
              child: Container(
                padding: const EdgeInsets.fromLTRB(8, 8, 10, 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    width: 1,
                    color: AppColors.appBorderColor,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 2),
                      child: SvgPicture.asset(
                        'assets/svgs/filter.svg',
                        width: 24,
                        height: 24,
                        colorFilter: const ColorFilter.mode(
                          AppColors.appBlackColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        getStatusValue(valueSelected, l10n),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 2),
                      child: SvgPicture.asset(
                        'assets/svgs/arrow-down-icon.svg',
                        colorFilter: const ColorFilter.mode(
                          AppColors.darkPrimaryBackgroundColor,
                          BlendMode.srcIn,
                        ),
                        width: 16,
                        height: 16,
                      ),
                    )
                  ],
                ),
              ),
              itemBuilder: (_) => (list ?? []).map((OrderHistoryFilterStatus value) {
                return PopupMenuItem(
                  value: value,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        getStatusValue(value, l10n),
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.4,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (value == valueSelected)
                        Padding(
                          padding: const EdgeInsets.only(left: 6.0),
                          child: SvgPicture.asset(
                            'assets/svgs/check-calendar.svg',
                            colorFilter: const ColorFilter.mode(
                              AppColors.darkPrimaryBackgroundColor,
                              BlendMode.srcIn,
                            ),
                          ),
                        )
                    ],
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }

  String getStatusValue(OrderHistoryFilterStatus? value, AppLocalizations l10n) {
    switch (value) {
      case OrderHistoryFilterStatus.ALL:
        return l10n.all;
      case OrderHistoryFilterStatus.COMPLETED:
        return l10n.completed;
      case OrderHistoryFilterStatus.CANCELLED:
        return l10n.cancelledStatus;
      default:
        return l10n.all;
    }
  }

  Widget _buildBody(BuildContext context, AppLocalizations l10n, bool isTablet) {
    return BlocConsumer<OrderHistoryCubit, OrderHistoryState>(
      listener: (context, state) {
        if (state.status == OrderHistoryStatus.Initial) {
          _searchController.clear();
        }
      },
      builder: (context, state) {
        final orders = state.onChangedSearch();
        // final orders = state.orders ?? [];
        final stt = state.status;

        if (stt == OrderHistoryStatus.Error) {
          return _buildError(l10n);
        } else if (orders.isEmpty && stt == OrderHistoryStatus.Success) {
          return _buildEmpty(context, l10n);
        } else {
          return const SafeArea(
            bottom: false,
            child: OrderHistoryPhoneLayout(),
          );
        }
      },
    );
  }

  Widget _buildError(AppLocalizations l10n) {
    return Center(
      child: Column(
        children: [
          SvgPicture.asset('assets/svgs/orders-history-cart.svg'),
          Padding(
            padding: const EdgeInsets.only(top: 8, bottom: 32),
            child: Text(
              l10n.somethingWentWrong,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF707070),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmpty(BuildContext context, AppLocalizations l10n) {
    return RefreshIndicator(
      onRefresh: () => _onRefresh(context),
      color: AppColors.appColor,
      child: CustomScrollView(
        primary: false,
        physics: const AlwaysScrollableScrollPhysics(
          parent: ClampingScrollPhysics(),
        ),
        slivers: [
          SliverList(
            delegate: SliverChildListDelegate([
              SizedBox(
                // height: MediaQuery.of(context).size.height / 1.5,
                child: Center(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CategoriesEmptyWidget(
                          errMsg: l10n.youDontHaveAnyOrderHistoryYet,
                          iconName: 'orders-history-cart',
                          widthIcon: 148,
                          heightIcon: 148,
                          fontSize: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ]),
          ),
        ],
      ),
    );
  }

  Future<void> _onRefresh(BuildContext context) async {
    context.read<OrderHistoryCubit>()
      ..onChangedSearchTextClear()
      ..onChangedPageClear()
      ..onChangedStatus(OrderHistoryFilterStatus.ALL)
      ..onChangedDateTimeType(OrderHistoryFilterDateTime.ALL)
      ..getOrders(page: 0);
  }
}
