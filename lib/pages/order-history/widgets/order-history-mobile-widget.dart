// ignore_for_file: inference_failure_on_instance_creation

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail/order-history-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-history/orders-history.cubit.dart';
import 'package:stickyqrbusiness/pages/order-history/widgets/@order-history-widgets.dart';

class OrderHistoryPhoneLayout extends StatelessWidget {
  const OrderHistoryPhoneLayout({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderHistoryCubit, OrderHistoryState>(
      builder: (context, state) {
        if (state.errorMsg != null) {
          return Center(child: Text('Error: ${state.errorMsg}'));
        }

        return OrdersList(
          orders: state.onChangedSearch(),
          // orders: state.orders ?? [],
          onOrderSelected: (order) {
            FocusManager.instance.primaryFocus?.unfocus();
            context.read<OrderHistoryDetailCubit>().getOrderHistoryDetail(id: order.id ?? '');
            Navigator.push(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) => MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => OrderHistoryCubit(),
                    ),
                  ],
                  child: OrderDetailsScreen(
                    order: order,
                    isMobile: true,
                  ),
                ),
                transitionDuration: const Duration(milliseconds: 200),
                reverseTransitionDuration: const Duration(milliseconds: 200),
                transitionsBuilder: (context, animation, secondaryAnimation, child) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(animation),
                    child: child,
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
