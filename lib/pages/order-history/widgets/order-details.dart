// ignore_for_file: inference_failure_on_instance_creation

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail/order-history-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-history/orders-history.cubit.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/history-refund/history-refund-page.dart';
import 'package:stickyqrbusiness/pages/order-history/widgets/@order-history-widgets.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/order-define.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';

class OrderDetailsScreen extends StatelessWidget {
  final Order order;
  final bool? isMobile;
  const OrderDetailsScreen({
    super.key,
    required this.order,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    return OrderDetailsScreenWidgetContent(
      order: order,
      isMobile: isMobile,
    );
  }
}

class OrderDetailsScreenWidgetContent extends StatelessWidget {
  final Order order;
  final bool? isMobile;

  const OrderDetailsScreenWidgetContent({
    Key? key,
    required this.order,
    this.isMobile = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    // return Scaffold(
    //   appBar: AppBar(
    //     elevation: 1,
    //     scrolledUnderElevation: 1,
    //     shadowColor: Colors.grey.withValues(alpha: .2),
    //     automaticallyImplyLeading: false,
    //     backgroundColor: AppColors.lightPrimaryBackgroundColor,
    //     centerTitle: false,
    //     toolbarHeight: 72,
    //     leadingWidth: 0,
    //     title: Row(
    //       children: [
    //         SizedBox(
    //           width: 48, //56
    //           child: MaterialButton(
    //             elevation: 0,
    //             highlightElevation: 0,
    //             hoverElevation: 0,
    //             hoverColor: AppColors.lightPrimaryBackgroundColor,
    //             onPressed: () => {
    //               Navigator.of(context).pop(),
    //             },
    //             color: AppColors.darkPrimaryBackgroundColor.withValues(alpha: .08),
    //             padding: EdgeInsets.zero,
    //             shape: const CircleBorder(),
    //             child: Padding(
    //               padding: const EdgeInsets.all(8.0),
    //               child: SvgPicture.asset(
    //                 'assets/svgs/close.svg',
    //                 colorFilter: const ColorFilter.mode(
    //                   AppColors.darkPrimaryBackgroundColor,
    //                   BlendMode.srcIn,
    //                 ),
    //               ),
    //             ),
    //           ),
    //         ),
    //         Expanded(child: OrderHeaderContent(l10n: l10n, order: order)),
    //       ],
    //     ),
    //   ),
    //   // appBar: isMobile == true
    //   //     ? AppBar(
    //   //         elevation: 1,
    //   //         scrolledUnderElevation: 1,
    //   //         shadowColor: Colors.grey.withValues(alpha: .2),
    //   //         automaticallyImplyLeading: false,
    //   //         backgroundColor: AppColors.lightPrimaryBackgroundColor,
    //   //         title: Text(
    //   //           l10n.odHistoryOrderDetail,
    //   //           style: const TextStyle(
    //   //             fontSize: 18,
    //   //             fontWeight: FontWeight.bold,
    //   //           ),
    //   //         ),
    //   //         centerTitle: true,
    //   //         leading: IconButton(
    //   //           icon: const Icon(Icons.arrow_back_ios_new),
    //   //           onPressed: () async {
    //   //             await Navigator.maybePop(context);
    //   //           },
    //   //         ),
    //   //       )
    //   //     : null,
    //   body: SafeArea(
    //     bottom: false,
    //     child: OrderDetails(order: order, l10n: l10n),
    //   ),
    // );

    final bool isTablet = MediaQuery.of(context).size.width >= 600;
    return SafeArea(
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            if (!isTablet) ...{
              AppBar(
                elevation: 0.5,
                scrolledUnderElevation: 0.5,
                shadowColor: Colors.grey.withValues(alpha: .2),
                automaticallyImplyLeading: false,
                backgroundColor: AppColors.lightPrimaryBackgroundColor,
                centerTitle: true,
                leading: IconButton(
                  icon: SvgPicture.asset('assets/svgs/arrow-back.svg'),
                  onPressed: () async {
                    await Navigator.maybePop(context);
                  },
                ),
                title: Text(
                  l10n.odHistoryOrderDetail,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              OrderHeaderContent(l10n: l10n, order: order),
              const SizedBox(height: 8),
              const Divider(
                thickness: 8,
                height: 8,
                color: AppColors.appBGAvatarColor,
              ),
            } else ...{
              OrderHeaderContent(l10n: l10n, order: order),
            },
            // Row(
            //   children: [
            //     SizedBox(
            //       width: 48, //56
            //       child: MaterialButton(
            //         elevation: 0,
            //         highlightElevation: 0,
            //         hoverElevation: 0,
            //         hoverColor: AppColors.lightPrimaryBackgroundColor,
            //         onPressed: () => {
            //           Navigator.of(context).pop(),
            //         },
            //         color: AppColors.darkPrimaryBackgroundColor.withValues(alpha: .08),
            //         padding: EdgeInsets.zero,
            //         shape: const CircleBorder(),
            //         child: Padding(
            //           padding: const EdgeInsets.all(8.0),
            //           child: SvgPicture.asset(
            //             'assets/svgs/close.svg',
            //             colorFilter: const ColorFilter.mode(
            //               AppColors.darkPrimaryBackgroundColor,
            //               BlendMode.srcIn,
            //             ),
            //           ),
            //         ),
            //       ),
            //     ),
            //     Expanded(child: OrderHeaderContent(l10n: l10n, order: order)),
            //   ],
            // ),
            Expanded(
              child: SingleChildScrollView(
                child: OrderDetails(order: order, l10n: l10n),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class OrderDetails extends StatelessWidget {
  final Order order;
  final AppLocalizations l10n;

  const OrderDetails({
    Key? key,
    required this.order,
    required this.l10n,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocSelector<OrderHistoryCubit, OrderHistoryState, OrderHistoryStatus>(
      selector: (state) {
        return state.status;
      },
      builder: (context, state) {
        switch (state) {
          case OrderHistoryStatus.Loading:
            return const OrderHistoryDetailLoading();
          default:
            return _buildContent(context);
        }
      },
    );
  }

  Widget _buildContent(BuildContext context) {
    if (order.id == null) {
      return Center(
        child: Text(l10n.selectedAnOrderToViewDetail),
      );
    }
    return OrderDetailWidgetContent(order: order);
  }
}

class OrderHeaderContent extends StatelessWidget {
  final Order order;
  final AppLocalizations l10n;

  const OrderHeaderContent({
    Key? key,
    required this.order,
    required this.l10n,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final orderNumber = '#${order.orderNumber}';
    final auth = context.read<AuthBloc>().state;
    final isShowAddNewDelivery = auth.business?.enableOrderDeliveryFeature == true && auth.business?.orderAllowDelivery == true;
    final bool isTablet = MediaQuery.of(context).size.width >= 600;

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        return BlocBuilder<OrderHistoryDetailCubit, OrderHistoryDetailState>(
          builder: (context, state) {
            final detail = state.order;
            final stt = order.status ?? '';
            final isAllowRefund = detail?.allowRefund ?? false;
            final isFromPOS = detail?.isFromPOS ?? false;
            AppLog.e('isFromPOS: $isFromPOS');
            return Container(
              padding: const EdgeInsets.fromLTRB(8, 8, 0, 8),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    width: 1,
                    color: AppColors.appBGAvatarColor,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (isTablet)
                  Container(
                    margin: const EdgeInsets.only(right: 16),
                    width: 48, //56
                    child: MaterialButton(
                      elevation: 0,
                      highlightElevation: 0,
                      hoverElevation: 0,
                      hoverColor: AppColors.lightPrimaryBackgroundColor,
                      onPressed: () => {
                        Navigator.of(context).pop(),
                      },
                      color: AppColors.darkPrimaryBackgroundColor.withValues(alpha: .08),
                      padding: EdgeInsets.zero,
                      shape: const CircleBorder(),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SvgPicture.asset(
                          'assets/svgs/close.svg',
                          colorFilter: const ColorFilter.mode(
                            AppColors.darkPrimaryBackgroundColor,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              orderNumber,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: AppColors.appBlackColor,
                              ),
                            ),
                            if (order.type?.toUpperCase() == OrderType.DELIVERY.name)
                              Padding(
                                padding: const EdgeInsets.only(left: 2.0),
                                child: SvgPicture.asset(
                                  'assets/svgs/delivery-black.svg',
                                ),
                              ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: OrderHistoryStatusWidget(order: order),
                        ),
                      ],
                    ),
                  ),
                  Wrap(
                    children: [
                      if (isShowAddNewDelivery) ...{
                        InkWell(
                          onTap: () {
                            // AppLog.d('stateAuth == ${jsonEncode(stateAuth.business)}');
                            // AppLog.e('order == ${jsonEncode(order)}');
                            AppBased.go(context, AppRoutes.addNewDeliveryPage, args: ScreenArguments(data: order));
                          },
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          child: Container(
                            // width: double.infinity,
                            height: 40,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            margin: const EdgeInsets.fromLTRB(0, 0, 8, 6),
                            decoration: BoxDecoration(
                              color: AppColors.lightPrimaryBackgroundColor,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                width: 1,
                                color: const Color(0xFF999CA0),
                              ),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  'assets/svgs/add.svg',
                                  colorFilter: const ColorFilter.mode(
                                    AppColors.appBlackColor,
                                    BlendMode.srcIn,
                                  ),
                                  width: 14,
                                  height: 14,
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Text(
                                    l10n.newDelivery,
                                    style: const TextStyle(
                                      color: AppColors.appBlackColor,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      },
                      // if (MediaQuery.of(context).size.width > 600)
                      if (stateAuth.checkPermissions(PermissionBusiness.orders_orders_history.name, [2, 3])) ...{
                        Row(
                          children: [
                            // if (order.status != null && order.status != '' && order.status?.toUpperCase() == 'COMPLETED')
                            if (isAllowRefund && stt != '' && stt.toUpperCase() != 'CANCELLED' && !isFromPOS)
                              Container(
                                height: 40,
                                margin: const EdgeInsets.only(right: 16),
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      PageRouteBuilder(
                                        pageBuilder: (context, animation, secondaryAnimation) => HistoryRefundPage(
                                          order: detail ?? order,
                                        ),
                                        transitionDuration: const Duration(milliseconds: 200),
                                        transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                          return SlideTransition(
                                            position: Tween<Offset>(
                                              begin: const Offset(1.0, 0.0),
                                              end: Offset.zero,
                                            ).animate(animation),
                                            child: child,
                                          );
                                        },
                                      ),
                                    );
                                  },
                                  style: ElevatedButton.styleFrom(
                                    shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(Radius.circular(12)),
                                    ),
                                    side: const BorderSide(
                                      width: 1,
                                      color: Color(0xFF999CA0),
                                    ),
                                  ),
                                  child: Text(
                                    l10n.refund,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.appBlackColor,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      }

                    ]
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
